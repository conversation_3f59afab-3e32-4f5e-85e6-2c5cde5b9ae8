#!/usr/bin/env python3
"""
PersonaCreator - <PERSON>ript Principal
<PERSON><PERSON><PERSON>á<PERSON> de personas baseado em comentários do YouTube

Uso:
    python main.py --channel-url "https://www.youtube.com/@canal" [opções]
"""

import argparse
import sys
from pathlib import Path
from typing import List, Dict, Any
from loguru import logger

# Configuração de logging - APENAS CONSOLE (sem arquivos na raiz)
from config import LOG_LEVEL, LOG_FORMAT
logger.remove()
logger.add(sys.stderr, level=LOG_LEVEL, format=LOG_FORMAT)
# Log de arquivo removido - cada projeto terá seu próprio log

# Imports dos módulos
from database import DatabaseManager
from ai_utils import LLaMAClient
from agents.content_curator import ContentCuratorAgent
from agents.priority_analyzer import PriorityAnalyzerAgent
from agents.comment_extractor import CommentExtractorAgent
from agents.comment_classifier import CommentClassifierAgent
from agents.sentiment_analyzer import SentimentAnalyzerAgent
from agents.persona_generator import PersonaGeneratorAgent


class PersonaCreatorOrchestrator:
    """Orquestrador principal do sistema PersonaCreator"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.llama = LLaMAClient()
        
        # Inicializa agentes
        self.content_curator = ContentCuratorAgent(self.db, self.llama)
        self.priority_analyzer = PriorityAnalyzerAgent(self.db, self.llama)
        self.comment_extractor = CommentExtractorAgent(self.db)
        self.comment_classifier = CommentClassifierAgent(self.db, self.llama)
        self.sentiment_analyzer = SentimentAnalyzerAgent(self.db, self.llama)
        self.persona_generator = PersonaGeneratorAgent(self.db, self.llama)
        
        logger.info("PersonaCreator inicializado com sucesso")
    
    def run_full_workflow(self, channel_url: str, max_videos: int = 20,
                         selected_tags: List[str] = None, use_ai: bool = True, interactive: bool = False) -> Dict[str, Any]:
        """Executa workflow completo de análise de persona"""
        logger.info(f"Iniciando workflow completo para canal: {channel_url}")
        
        if selected_tags is None:
            selected_tags = ['filho', 'filha', 'familia', 'educacao']
        
        workflow_results = {
            'channel_url': channel_url,
            'workflow_steps': {},
            'success': True,
            'errors': []
        }
        
        try:
            # Etapa 1: Curadoria de Conteúdo
            if interactive:
                if not interactive_approval(
                    "CURADORIA DE CONTEÚDO",
                    f"• Extrair vídeos do canal: {channel_url}\n"
                    f"• Analisar relevância para parentalidade/família\n"
                    f"• Filtrar até {max_videos} vídeos mais relevantes\n"
                    f"• Salvar no banco de dados"
                ):
                    workflow_results['success'] = False
                    workflow_results['errors'].append("Etapa cancelada pelo usuário")
                    return workflow_results

            logger.info("=== ETAPA 1: CURADORIA DE CONTEÚDO ===")
            curation_result = self.content_curator.run_curation_workflow(channel_url)
            workflow_results['workflow_steps']['curation'] = curation_result

            if not curation_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na curadoria de conteúdo")
                return workflow_results

            channel_id = curation_result['channel_id']
            logger.info(f"Curadoria concluída: {curation_result['relevant_videos']} vídeos relevantes")

            if interactive:
                show_step_results(
                    "CURADORIA DE CONTEÚDO",
                    {
                        "Vídeos relevantes encontrados": curation_result['relevant_videos'],
                        "Total de vídeos analisados": curation_result['total_videos'],
                        "Canal ID": channel_id
                    },
                    ["output/relevant_videos.json"]
                )
            
            # Etapa 2: Priorização por Relevância
            if interactive:
                if not interactive_approval(
                    "PRIORIZAÇÃO POR RELEVÂNCIA",
                    f"• Analisar {curation_result['relevant_videos']} vídeos relevantes\n"
                    f"• Calcular scores de prioridade baseados em:\n"
                    f"  - Número de comentários\n"
                    f"  - Engajamento (likes, views)\n"
                    f"  - Relevância do conteúdo\n"
                    f"• Ordenar vídeos por prioridade",
                    f"✅ {curation_result['relevant_videos']} vídeos relevantes encontrados"
                ):
                    workflow_results['success'] = False
                    workflow_results['errors'].append("Etapa cancelada pelo usuário")
                    return workflow_results

            logger.info("=== ETAPA 2: PRIORIZAÇÃO POR RELEVÂNCIA ===")
            priority_result = self.priority_analyzer.run_prioritization_workflow(channel_id)
            workflow_results['workflow_steps']['prioritization'] = priority_result

            if not priority_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na priorização")
                return workflow_results

            logger.info("Priorização concluída")

            if interactive:
                show_step_results(
                    "PRIORIZAÇÃO POR RELEVÂNCIA",
                    {
                        "Vídeos priorizados": priority_result.get('videos_prioritized', 0)
                    },
                    ["output/prioritized_videos.json"]
                )
            
            # Etapa 3: Extração de Comentários
            if interactive:
                if not interactive_approval(
                    "EXTRAÇÃO DE COMENTÁRIOS",
                    f"• Extrair comentários dos vídeos priorizados\n"
                    f"• Filtrar comentários relevantes sobre parentalidade\n"
                    f"• Limitar a {max_videos} vídeos mais prioritários\n"
                    f"• Salvar comentários no banco de dados",
                    f"✅ Vídeos priorizados e prontos para extração"
                ):
                    workflow_results['success'] = False
                    workflow_results['errors'].append("Etapa cancelada pelo usuário")
                    return workflow_results

            logger.info("=== ETAPA 3: EXTRAÇÃO DE COMENTÁRIOS ===")
            extraction_result = self.comment_extractor.run_extraction_workflow(channel_id, max_videos)
            workflow_results['workflow_steps']['extraction'] = extraction_result

            if not extraction_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na extração de comentários")
                return workflow_results

            logger.info(f"Extração concluída: {extraction_result['total_comments_extracted']} comentários")

            if interactive:
                show_step_results(
                    "EXTRAÇÃO DE COMENTÁRIOS",
                    {
                        "Comentários extraídos": extraction_result['total_comments_extracted'],
                        "Vídeos processados": extraction_result.get('videos_processed', 0)
                    },
                    ["output/comments_summary.json"]
                )
            
            # Etapa 4: Classificação de Comentários
            if interactive:
                if not interactive_approval(
                    "CLASSIFICAÇÃO DE COMENTÁRIOS",
                    f"• Classificar {extraction_result['total_comments_extracted']} comentários por categoria:\n"
                    f"  - Dúvidas sobre educação\n"
                    f"  - Experiências pessoais\n"
                    f"  - Pedidos de ajuda\n"
                    f"  - Compartilhamento de dicas\n"
                    f"• Usar IA para classificação automática" + (" (ativada)" if use_ai else " (desativada)"),
                    f"✅ {extraction_result['total_comments_extracted']} comentários extraídos"
                ):
                    workflow_results['success'] = False
                    workflow_results['errors'].append("Etapa cancelada pelo usuário")
                    return workflow_results

            logger.info("=== ETAPA 4: CLASSIFICAÇÃO DE COMENTÁRIOS ===")
            classification_result = self.comment_classifier.run_classification_workflow(channel_id, use_ai)
            workflow_results['workflow_steps']['classification'] = classification_result

            if not classification_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na classificação")
                return workflow_results

            logger.info("Classificação concluída")

            if interactive:
                show_step_results(
                    "CLASSIFICAÇÃO DE COMENTÁRIOS",
                    {
                        "Comentários classificados": classification_result.get('comments_classified', 0)
                    },
                    ["output/classified_comments.json"]
                )
            
            # Etapa 5: Análise de Sentimentos
            if interactive:
                if not interactive_approval(
                    "ANÁLISE DE SENTIMENTOS",
                    f"• Analisar sentimentos dos comentários classificados\n"
                    f"• Identificar padrões emocionais:\n"
                    f"  - Frustração, ansiedade, alegria\n"
                    f"  - Dúvidas, medos, conquistas\n"
                    f"• Filtrar por tags: {', '.join(selected_tags)}\n"
                    f"• Gerar insights sobre o público",
                    f"✅ Comentários classificados e prontos para análise"
                ):
                    workflow_results['success'] = False
                    workflow_results['errors'].append("Etapa cancelada pelo usuário")
                    return workflow_results

            logger.info("=== ETAPA 5: ANÁLISE DE SENTIMENTOS ===")
            sentiment_result = self.sentiment_analyzer.run_analysis_workflow(
                channel_id, selected_tags, min_confidence=0.5
            )
            workflow_results['workflow_steps']['sentiment_analysis'] = sentiment_result

            if not sentiment_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na análise de sentimentos")
                return workflow_results

            logger.info("Análise de sentimentos concluída")

            if interactive:
                show_step_results(
                    "ANÁLISE DE SENTIMENTOS",
                    {
                        "Comentários analisados": sentiment_result.get('comments_analyzed', 0),
                        "Insights gerados": sentiment_result.get('insights_count', 0)
                    },
                    ["output/analysis_summary.json"]
                )
            
            # Etapa 6: Geração de Persona
            if interactive:
                if not interactive_approval(
                    "GERAÇÃO DE PERSONA",
                    f"• Consolidar todos os dados coletados\n"
                    f"• Gerar persona detalhada baseada em:\n"
                    f"  - Padrões de comentários\n"
                    f"  - Análise de sentimentos\n"
                    f"  - Classificações identificadas\n"
                    f"• Criar relatório final com insights\n"
                    f"• Exportar persona em formato JSON",
                    f"✅ Análise de sentimentos concluída com insights"
                ):
                    workflow_results['success'] = False
                    workflow_results['errors'].append("Etapa cancelada pelo usuário")
                    return workflow_results

            logger.info("=== ETAPA 6: GERAÇÃO DE PERSONA ===")
            persona_result = self.persona_generator.run_persona_generation_workflow(channel_id)
            workflow_results['workflow_steps']['persona_generation'] = persona_result

            if not persona_result['success']:
                workflow_results['success'] = False
                workflow_results['errors'].append("Falha na geração de persona")
                return workflow_results

            logger.info("Geração de persona concluída")

            if interactive:
                show_step_results(
                    "GERAÇÃO DE PERSONA",
                    {
                        "Persona gerada": "Sim" if persona_result['success'] else "Não",
                        "Confiança": f"{persona_result.get('confidence_score', 0):.1%}",
                        "Arquivo do relatório": persona_result.get('report_file', 'N/A')
                    },
                    [persona_result.get('report_file', 'output/persona_report.json')]
                )
            
            # Resumo final
            workflow_results['final_summary'] = {
                'channel_id': channel_id,
                'total_videos_analyzed': curation_result['total_videos'],
                'relevant_videos': curation_result['relevant_videos'],
                'total_comments_extracted': extraction_result['total_comments_extracted'],
                'persona_confidence': persona_result['confidence_score'],
                'persona_report_file': persona_result['report_file']
            }
            
            logger.info("=== WORKFLOW COMPLETO CONCLUÍDO COM SUCESSO ===")
            
        except Exception as e:
            logger.error(f"Erro no workflow: {e}")
            workflow_results['success'] = False
            workflow_results['errors'].append(str(e))
        
        return workflow_results
    
    def run_partial_workflow(self, step: str, channel_id: int = None, 
                           channel_url: str = None, **kwargs) -> Dict[str, Any]:
        """Executa apenas uma etapa específica do workflow"""
        logger.info(f"Executando etapa: {step}")
        
        try:
            if step == "curation" and channel_url:
                return self.content_curator.run_curation_workflow(channel_url)
            
            elif step == "prioritization" and channel_id:
                return self.priority_analyzer.run_prioritization_workflow(channel_id)
            
            elif step == "extraction" and channel_id:
                max_videos = kwargs.get('max_videos', 20)
                return self.comment_extractor.run_extraction_workflow(channel_id, max_videos)
            
            elif step == "classification" and channel_id:
                use_ai = kwargs.get('use_ai', True)
                return self.comment_classifier.run_classification_workflow(channel_id, use_ai)
            
            elif step == "sentiment_analysis" and channel_id:
                selected_tags = kwargs.get('selected_tags', ['filho', 'filha', 'familia'])
                min_confidence = kwargs.get('min_confidence', 0.5)
                return self.sentiment_analyzer.run_analysis_workflow(
                    channel_id, selected_tags, min_confidence
                )
            
            elif step == "persona_generation" and channel_id:
                return self.persona_generator.run_persona_generation_workflow(channel_id)
            
            else:
                return {
                    'success': False,
                    'error': f'Etapa "{step}" não reconhecida ou parâmetros insuficientes'
                }
                
        except Exception as e:
            logger.error(f"Erro na etapa {step}: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_workflow_status(self, channel_id: int) -> Dict[str, Any]:
        """Verifica status do workflow para um canal"""
        with self.db.get_connection() as conn:
            # Verifica vídeos
            cursor = conn.execute(
                "SELECT COUNT(*) as total, SUM(is_relevant) as relevant FROM videos WHERE channel_id = ?",
                (channel_id,)
            )
            video_stats = dict(cursor.fetchone())
            
            # Verifica comentários
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT c.id) as total_comments
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
            """, (channel_id,))
            comment_stats = dict(cursor.fetchone())
            
            # Verifica tags
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT ct.comment_id) as tagged_comments
                FROM comment_tags ct
                JOIN comments c ON ct.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
            """, (channel_id,))
            tag_stats = dict(cursor.fetchone())
            
            # Verifica análises
            cursor = conn.execute("""
                SELECT COUNT(DISTINCT ca.comment_id) as analyzed_comments
                FROM comment_analysis ca
                JOIN comments c ON ca.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
            """, (channel_id,))
            analysis_stats = dict(cursor.fetchone())
            
            # Verifica personas
            cursor = conn.execute(
                "SELECT COUNT(*) as personas FROM personas WHERE channel_id = ?",
                (channel_id,)
            )
            persona_stats = dict(cursor.fetchone())
        
        return {
            'channel_id': channel_id,
            'videos': video_stats,
            'comments': comment_stats,
            'tags': tag_stats,
            'analyses': analysis_stats,
            'personas': persona_stats,
            'workflow_complete': all([
                (video_stats.get('relevant') or 0) > 0,
                (comment_stats.get('total_comments') or 0) > 0,
                (tag_stats.get('tagged_comments') or 0) > 0,
                (analysis_stats.get('analyzed_comments') or 0) > 0,
                (persona_stats.get('personas') or 0) > 0
            ])
        }


def interactive_approval(step_name: str, step_description: str, results_summary: str = None) -> bool:
    """
    Solicita aprovação do usuário para continuar para a próxima etapa

    Args:
        step_name: Nome da etapa
        step_description: Descrição do que será feito
        results_summary: Resumo dos resultados da etapa anterior

    Returns:
        True se aprovado, False se cancelado
    """
    print("\n" + "="*60)
    print(f"🎯 PRÓXIMA ETAPA: {step_name.upper()}")
    print("="*60)

    if results_summary:
        print(f"📊 RESULTADOS DA ETAPA ANTERIOR:")
        print(results_summary)
        print("-"*60)

    print(f"📋 O QUE SERÁ FEITO:")
    print(step_description)
    print("-"*60)

    while True:
        choice = input("\n🤔 Deseja continuar? [s/n/d/q]: ").lower().strip()

        if choice in ['s', 'sim', 'y', 'yes', '']:
            print("✅ Continuando...")
            return True
        elif choice in ['n', 'nao', 'não', 'no']:
            print("⏸️  Etapa pulada pelo usuário")
            return False
        elif choice in ['d', 'detalhes', 'details']:
            print("\n📋 OPÇÕES DISPONÍVEIS:")
            print("  s/sim/y/yes - Continuar com a etapa")
            print("  n/não/no    - Pular esta etapa")
            print("  d/detalhes  - Mostrar estas opções")
            print("  q/quit      - Sair do programa")
            continue
        elif choice in ['q', 'quit', 'sair']:
            print("🚪 Saindo do programa...")
            sys.exit(0)
        else:
            print("❌ Opção inválida. Use: s (sim), n (não), d (detalhes), q (sair)")


def show_step_results(step_name: str, results: dict, output_files: list = None):
    """
    Mostra os resultados de uma etapa de forma organizada

    Args:
        step_name: Nome da etapa
        results: Dicionário com os resultados
        output_files: Lista de arquivos gerados
    """
    print(f"\n✅ ETAPA CONCLUÍDA: {step_name.upper()}")
    print("="*50)

    # Mostra resultados principais
    for key, value in results.items():
        if isinstance(value, (int, float)):
            print(f"📊 {key}: {value}")
        elif isinstance(value, str) and len(value) < 100:
            print(f"📝 {key}: {value}")
        elif isinstance(value, list):
            print(f"📋 {key}: {len(value)} itens")

    # Mostra arquivos gerados
    if output_files:
        print(f"\n📁 ARQUIVOS GERADOS:")
        for file in output_files:
            if Path(file).exists():
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} (não encontrado)")

    print("-"*50)


def main():
    """Função principal do script"""
    parser = argparse.ArgumentParser(
        description="PersonaCreator - Criação conversacional de personas baseada em comentários do YouTube"
    )
    
    parser.add_argument(
        '--channel-url',
        help='URL do canal do YouTube para análise'
    )
    

    
    parser.add_argument(
        '--step',
        choices=['curation', 'prioritization', 'extraction', 'classification', 'sentiment_analysis', 'persona_generation'],
        help='Executar apenas uma etapa específica'
    )
    
    parser.add_argument(
        '--channel-id',
        type=int,
        help='ID do canal no banco (necessário para etapas específicas)'
    )
    
    parser.add_argument(
        '--no-ai',
        action='store_true',
        help='Desabilita uso de IA (usa métodos simples)'
    )

    parser.add_argument(
        '--status',
        action='store_true',
        help='Mostra status do workflow para um canal'
    )
    
    args = parser.parse_args()
    
    # Inicializa orquestrador
    orchestrator = PersonaCreatorOrchestrator()
    
    try:
        # Modo conversacional é o padrão
        if not args.step and not args.status:
            # Modo conversacional (padrão)
            from agents.conversational_orchestrator import ConversationalOrchestrator
            from database import DatabaseManager
            from ai_utils import LLaMAClient

            db_manager = DatabaseManager()
            db_manager.init_database()
            llama_client = LLaMAClient()

            chat_orchestrator = ConversationalOrchestrator(db_manager, llama_client)
            chat_orchestrator.start_conversation(args.channel_url)
            return

        elif args.status and args.channel_id:
            # Mostra status
            status = orchestrator.get_workflow_status(args.channel_id)
            print(f"\n=== STATUS DO WORKFLOW - Canal {args.channel_id} ===")
            print(f"Vídeos: {status['videos']['total']} total, {status['videos']['relevant']} relevantes")
            print(f"Comentários: {status['comments']['total_comments']}")
            print(f"Comentários com tags: {status['tags']['tagged_comments']}")
            print(f"Comentários analisados: {status['analyses']['analyzed_comments']}")
            print(f"Personas geradas: {status['personas']['personas']}")
            print(f"Workflow completo: {'✓' if status['workflow_complete'] else '✗'}")
            
        elif args.step:
            # Executa etapa específica
            result = orchestrator.run_partial_workflow(
                step=args.step,
                channel_id=args.channel_id,
                channel_url=args.channel_url,
                max_videos=args.max_videos,
                selected_tags=args.tags,
                use_ai=not args.no_ai
            )
            
            if result['success']:
                print(f"\n✓ Etapa '{args.step}' concluída com sucesso!")
                logger.info(f"Resultado: {result}")
            else:
                print(f"\n✗ Erro na etapa '{args.step}': {result.get('error', 'Erro desconhecido')}")
                sys.exit(1)
        
        else:
            # Fallback: se não especificou etapa nem status, usa modo conversacional
            print("💡 Dica: Use --step para executar etapas específicas ou --status para ver status")
            print("🚀 Iniciando modo conversacional...")

            from agents.conversational_orchestrator import ConversationalOrchestrator
            from database import DatabaseManager
            from ai_utils import LLaMAClient

            db_manager = DatabaseManager()
            db_manager.init_database()
            llama_client = LLaMAClient()

            chat_orchestrator = ConversationalOrchestrator(db_manager, llama_client)
            chat_orchestrator.start_conversation(args.channel_url)
    
    except KeyboardInterrupt:
        print("\n\n⚠️  Workflow interrompido pelo usuário")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Erro fatal: {e}")
        print(f"\n💥 Erro fatal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
