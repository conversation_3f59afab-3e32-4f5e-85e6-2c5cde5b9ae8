"""
Configurações Personalizáveis pelo Usuário
Defina aqui o contexto, palavras-chave e tags específicas para sua análise
"""

# ========================================
# CONFIGURAÇÃO DO CONTEXTO DE ANÁLISE
# ========================================

# Defina o nicho/contexto que você quer analisar
ANALYSIS_CONTEXT = {
    "name": "Parentalidade e Autismo",  # Nome do seu nicho
    "description": "Análise de conteúdo sobre pais de crianças autistas",
    "target_audience": "Pais e mães de crianças com autismo"
}

# Palavras-chave que definem RELEVÂNCIA para seu contexto
# O sistema só considerará vídeos relevantes se contiverem essas palavras
RELEVANCE_KEYWORDS = [
    # Palavras principais do seu nicho
    "autismo", "autista", "TEA", "espectro autista",
    "neurodivergente", "neuroatípico",
    
    # Contexto familiar
    "pais", "mães", "família", "criança", "filho", "filha",
    
    # Temas relacionados
    "terapia", "desenvolvimento", "comportamento", 
    "educação especial", "inclusão", "estimulação"
]

# ========================================
# TAGS PERSONALIZADAS PARA CLASSIFICAÇÃO
# ========================================

# Defina as tags que você quer que o sistema identifique nos comentários
CUSTOM_COMMENT_TAGS = {
    # Tags sobre pessoas
    "filho_autista": [
        "meu filho autista", "filho com autismo", "criança autista",
        "menino autista", "filho TEA", "meu autista"
    ],
    
    "filha_autista": [
        "minha filha autista", "filha com autismo", "menina autista", 
        "filha TEA", "minha autista"
    ],
    
    # Tags sobre terapias
    "terapia": [
        "terapia", "terapeuta", "ABA", "fonoaudiologia", "psicologia",
        "terapia ocupacional", "fisioterapia"
    ],
    
    # Tags sobre comportamento
    "comportamento": [
        "comportamento", "birra", "crise", "meltdown", "shutdown",
        "estereotipia", "estimming", "autoagressão"
    ],
    
    # Tags sobre desenvolvimento
    "desenvolvimento": [
        "desenvolvimento", "atraso", "evolução", "progresso",
        "habilidades", "comunicação", "fala", "linguagem"
    ],
    
    # Tags sobre educação
    "educacao": [
        "escola", "professora", "educação", "inclusão", "AEE",
        "sala de recursos", "adaptação", "currículo"
    ],
    
    # Tags sobre saúde
    "saude": [
        "médico", "neurologista", "psiquiatra", "pediatra",
        "diagnóstico", "medicação", "exames"
    ],
    
    # Tags sobre desafios
    "desafios": [
        "dificuldade", "desafio", "problema", "preocupação",
        "medo", "ansiedade", "estresse", "cansaço"
    ]
}

# ========================================
# CATEGORIAS DE ANÁLISE DE SENTIMENTOS
# ========================================

# Defina as categorias específicas que você quer analisar
CUSTOM_ANALYSIS_CATEGORIES = {
    # Dores específicas do seu nicho
    "dores": [
        # Dores gerais
        "problema", "dificuldade", "preocupação", "medo", "ansiedade",
        "estresse", "cansaço", "frustração", "exaustão",
        
        # Dores específicas do autismo
        "diagnóstico tardio", "falta de apoio", "preconceito", "exclusão",
        "não aceita o diagnóstico", "negação", "culpa", "isolamento",
        "falta de terapeutas", "lista de espera", "caro", "sem dinheiro"
    ],
    
    # Desejos específicos
    "desejos": [
        # Desejos gerais
        "quero", "desejo", "sonho", "espero", "gostaria", "preciso",
        
        # Desejos específicos
        "que meu filho fale", "que seja independente", "que seja aceito",
        "encontrar bons terapeutas", "escola inclusiva", "mais apoio",
        "que as pessoas entendam", "diagnóstico precoce"
    ],
    
    # Sonhos e aspirações
    "sonhos": [
        "sonho", "futuro", "esperança", "independência", "autonomia",
        "que seja feliz", "que tenha amigos", "que estude", "que trabalhe",
        "vida normal", "seja aceito", "realize seus sonhos"
    ],
    
    # Frustrações específicas
    "frustracoes": [
        "frustração", "decepção", "raiva", "revolta", "injustiça",
        "sistema falho", "falta de informação", "demora no diagnóstico",
        "profissionais despreparados", "escola não ajuda"
    ],
    
    # Palavras que geram engajamento positivo
    "palavras_magicas": [
        # Palavras de apoio
        "força", "coragem", "esperança", "fé", "amor", "carinho",
        "compreensão", "paciência", "dedicação", "luta",
        
        # Palavras de resultado
        "evolução", "progresso", "conquista", "vitória", "superação",
        "orgulho", "gratidão", "milagre", "transformação"
    ],
    
    # Oportunidades de negócio específicas
    "oportunidades": [
        "falta no mercado", "deveria existir", "precisamos de",
        "seria útil", "ninguém oferece", "difícil de encontrar",
        "curso para pais", "material adaptado", "brinquedo terapêutico",
        "app para autismo", "rede de apoio", "grupo de pais"
    ]
}

# ========================================
# CONFIGURAÇÕES DE PRIORIZAÇÃO
# ========================================

# Defina quais tipos de vídeo têm prioridade na análise
PRIORITY_INDICATORS = {
    # Palavras que indicam alta prioridade
    "high_priority": [
        "diagnóstico", "primeiros sinais", "como lidar", "dicas",
        "experiência", "relato", "jornada", "superação"
    ],
    
    # Palavras que indicam discussões emocionais (bom para análise)
    "emotional_content": [
        "choro", "emoção", "difícil", "desafio", "luta", "força",
        "esperança", "medo", "ansiedade", "preocupação"
    ],
    
    # Tipos de conteúdo prioritários
    "content_types": [
        "depoimento", "relato", "experiência", "história real",
        "dia a dia", "rotina", "dicas práticas", "como fazer"
    ]
}

# ========================================
# CONFIGURAÇÕES DE FILTROS
# ========================================

# Defina critérios para filtrar comentários relevantes
COMMENT_FILTERS = {
    # Tamanho mínimo do comentário (caracteres)
    "min_length": 30,
    
    # Palavras que indicam spam ou irrelevância
    "spam_indicators": [
        "primeiro", "primeira", "like se", "quem concorda",
        "inscreva-se", "canal", "vídeo incrível", "parabéns pelo vídeo"
    ],
    
    # Palavras que indicam alta relevância
    "relevance_boosters": [
        "meu filho", "minha filha", "minha experiência", "passei por isso",
        "também tenho", "aqui em casa", "comigo foi assim"
    ]
}
