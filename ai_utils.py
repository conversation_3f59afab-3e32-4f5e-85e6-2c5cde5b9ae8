"""
Utilitários de IA - Versão Simplificada (Apenas OpenAI)
"""
import os
import json
from typing import Dict, List, Any, Optional
from loguru import logger
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

# Importa OpenAI
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.error("OpenAI não está instalado. Use: pip install openai")

# Configurações
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")


class LLaMAClient:
    """Cliente simplificado para OpenAI"""
    
    def __init__(self):
        if not OPENAI_AVAILABLE:
            raise ValueError("OpenAI não está instalado. Use: pip install openai")
        
        if not OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY não encontrada. Configure no arquivo .env")
        
        self.client = OpenAI(api_key=OPENAI_API_KEY)
        self.model = "gpt-4o-mini"
        logger.info(f"Inicializando com OpenAI - modelo: {self.model}")
    
    def generate_response(self, prompt: str, system_prompt: str = None,
                         temperature: float = 0.7, max_tokens: int = 1500) -> str:
        """Gera resposta usando OpenAI"""
        try:
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            messages.append({"role": "user", "content": prompt})
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"Erro na geração de resposta: {e}")
            return ""


class AnalysisPrompts:
    """Prompts para análise de conteúdo"""
    
    @staticmethod
    def analyze_video_relevance(title: str, description: str, keywords: List[str]) -> str:
        """Prompt para analisar relevância de vídeo"""
        keywords_str = ", ".join(keywords)
        
        return f"""Analise se este vídeo é relevante para o contexto especificado.

Título: "{title}"
Descrição: "{description}"
Palavras-chave do contexto: {keywords_str}

IMPORTANTE: Retorne APENAS um JSON válido, sem texto adicional.

Formato obrigatório:
{{
    "relevante": true,
    "score_relevancia": 0.8,
    "justificativa": "Explica por que é relevante",
    "palavras_encontradas": ["palavra1", "palavra2"]
}}

Se não for relevante, retorne: {{"relevante": false, "score_relevancia": 0.0, "justificativa": "Não relacionado ao contexto", "palavras_encontradas": []}}"""

    @staticmethod
    def prioritize_videos(videos_data: List[Dict]) -> str:
        """Prompt para priorizar vídeos por relevância"""
        videos_info = []
        for i, video in enumerate(videos_data):
            videos_info.append(f"{i+1}. Título: {video['title']} | Views: {video.get('view_count', 0)} | Comentários: {video.get('comment_count', 0)}")
        
        videos_str = "\n".join(videos_info)
        
        return f"""Analise os vídeos e ordene-os por prioridade para análise de comentários.
Considere: relevância do título, número de views, comentários, potencial para discussões.

Vídeos:
{videos_str}

IMPORTANTE: Retorne APENAS um JSON válido, sem texto adicional.

Formato obrigatório:
{{
    "ranking": [3, 1, 2],
    "scores": [0.9, 0.7, 0.8],
    "justificativas": ["Mais relevante", "Bom engajamento", "Tema importante"]
}}

O ranking deve conter os números das posições originais ordenados por prioridade."""

    @staticmethod
    def classify_comment_tags(comment: str, available_tags: Dict[str, List[str]]) -> str:
        """Prompt para classificar comentários com tags"""
        tags_info = []
        for tag, keywords in available_tags.items():
            tags_info.append(f"- {tag}: {', '.join(keywords)}")
        
        tags_str = "\n".join(tags_info)
        
        return f"""Analise o comentário e identifique quais tags se aplicam baseado nas palavras-chave.

Comentário: "{comment}"

Tags disponíveis:
{tags_str}

IMPORTANTE: Retorne APENAS um JSON válido, sem texto adicional.

Formato obrigatório:
{{
    "tags_encontradas": [
        {{
            "tag": "nome_da_tag",
            "confianca": 0.8,
            "palavras_encontradas": ["palavra1", "palavra2"]
        }}
    ]
}}

Se nenhuma tag se aplicar, retorne: {{"tags_encontradas": []}}"""

    @staticmethod
    def extract_pain_points(comment: str) -> str:
        """Prompt para extrair dores e frustrações"""
        return f"""Analise o comentário e extraia dores, preocupações, medos e frustrações.

Comentário: "{comment}"

IMPORTANTE: Retorne APENAS um JSON válido, sem texto adicional.

Formato obrigatório:
{{
    "dores_identificadas": ["dor1", "dor2"],
    "nivel_intensidade": "baixo",
    "categoria_principal": "comportamento",
    "confianca": 0.8
}}

Se não encontrar dores relevantes, retorne: {{"dores_identificadas": [], "nivel_intensidade": "baixo", "categoria_principal": "", "confianca": 0.0}}"""

    @staticmethod
    def extract_desires_dreams(comment: str) -> str:
        """Prompt para extrair desejos e sonhos"""
        return f"""Analise o comentário e extraia desejos, sonhos, objetivos e aspirações.

Comentário: "{comment}"

IMPORTANTE: Retorne APENAS um JSON válido, sem texto adicional.

Formato obrigatório:
{{
    "desejos": ["desejo1", "desejo2"],
    "sonhos": ["sonho1", "sonho2"],
    "objetivos": ["objetivo1", "objetivo2"],
    "categoria_principal": "educação",
    "confianca": 0.8
}}

Se não encontrar aspirações relevantes, retorne: {{"desejos": [], "sonhos": [], "objetivos": [], "categoria_principal": "", "confianca": 0.0}}"""

    @staticmethod
    def extract_magic_words(comment: str) -> str:
        """Prompt para extrair palavras mágicas e oportunidades"""
        return f"""Analise o comentário e identifique:
1. Palavras e expressões que geram engajamento positivo
2. Oportunidades de negócio ou necessidades não atendidas
3. Linguagem que ressoa com o público

Comentário: "{comment}"

IMPORTANTE: Retorne APENAS um JSON válido, sem texto adicional.

Formato obrigatório:
{{
    "palavras_magicas": ["palavra1", "palavra2"],
    "oportunidades_negocio": ["oportunidade1", "oportunidade2"],
    "linguagem_efetiva": ["padrão1", "padrão2"],
    "confianca": 0.8
}}

Se não encontrar elementos relevantes, retorne: {{"palavras_magicas": [], "oportunidades_negocio": [], "linguagem_efetiva": [], "confianca": 0.0}}"""

    @staticmethod
    def generate_persona(analysis_summary: Dict[str, List]) -> str:
        """Prompt para gerar persona final"""
        summary_str = json.dumps(analysis_summary, indent=2, ensure_ascii=False)
        
        return f"""Com base na análise de comentários, crie uma persona detalhada do público-alvo.

Dados da análise:
{summary_str}

IMPORTANTE: Retorne APENAS um JSON válido, sem texto adicional.

Formato obrigatório:
{{
    "nome_persona": "Maria Educadora",
    "demografia": {{
        "idade": "30-40 anos",
        "localização": "Brasil",
        "situação_familiar": "casada",
        "filhos": "1-2 filhos",
        "renda": "Classe média"
    }},
    "dores_principais": ["dor1", "dor2", "dor3"],
    "desejos_principais": ["desejo1", "desejo2", "desejo3"],
    "sonhos_aspiracoes": ["sonho1", "sonho2"],
    "frustracoes_comuns": ["frustração1", "frustração2"],
    "palavras_magicas": ["palavra1", "palavra2"],
    "oportunidades_negocio": ["oportunidade1", "oportunidade2"],
    "canais_comunicacao": ["YouTube", "Instagram"],
    "tom_comunicacao": "acolhedor e educativo",
    "descricao_narrativa": "Descrição da persona em 2-3 parágrafos",
    "score_confianca": 0.8
}}

Baseie-se nos dados fornecidos para criar uma persona realista."""
