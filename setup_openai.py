#!/usr/bin/env python3
"""
Script para configurar a chave da API da OpenAI
"""

import os
from pathlib import Path

def setup_openai_key():
    """Configura a chave da API da OpenAI"""
    print("🔑 CONFIGURAÇÃO DA API OPENAI")
    print("="*40)
    
    # Verifica se já existe um arquivo .env
    env_file = Path(".env")
    
    if env_file.exists():
        print("📄 Arquivo .env já existe")
        with open(env_file, 'r') as f:
            content = f.read()
            if "OPENAI_API_KEY" in content and "sk-" in content:
                print("✅ Chave da OpenAI já configurada")
                return True
    
    print("\n💡 Para usar a OpenAI, você precisa:")
    print("1. Criar uma conta em: https://platform.openai.com/")
    print("2. Gerar uma API key em: https://platform.openai.com/api-keys")
    print("3. Adicionar créditos à sua conta")
    print("\n⚠️  IMPORTANTE: A API da OpenAI é paga, mas gpt-4o-mini é muito barato!")
    print("   Custo aproximado: $0.15 por 1M tokens de entrada")
    
    api_key = input("\n🔑 Cole sua chave da API da OpenAI (ou Enter para pular): ").strip()
    
    if not api_key:
        print("⏭️  Pulando configuração da OpenAI")
        print("   O sistema usará Ollama local se disponível")
        return False
    
    if not api_key.startswith("sk-"):
        print("❌ Chave inválida! Deve começar com 'sk-'")
        return False
    
    # Cria ou atualiza o arquivo .env
    env_content = f"""# Configurações da OpenAI
OPENAI_API_KEY={api_key}
USE_OPENAI=true
OPENAI_MODEL=gpt-4o-mini

# Configurações do Ollama (fallback)
LLAMA_MODEL_NAME=gemma3:12b
LLAMA_API_URL=http://localhost:11434

# Configurações de logging
LOG_LEVEL=INFO

# Configurações de análise
MAX_VIDEOS_PER_CHANNEL=50
MAX_COMMENTS_PER_VIDEO=500
"""
    
    with open(".env", "w") as f:
        f.write(env_content)
    
    print("✅ Chave da OpenAI configurada com sucesso!")
    print("📁 Arquivo .env criado")
    
    return True

def test_openai_connection():
    """Testa a conexão com a OpenAI"""
    print("\n🧪 Testando conexão com OpenAI...")
    
    try:
        from ai_utils import LLaMAClient
        
        client = LLaMAClient()
        
        if client.provider != "openai":
            print("⚠️  Sistema não está usando OpenAI")
            return False
        
        response = client.generate_response(
            "Responda apenas 'OK' se você está funcionando.",
            temperature=0.1,
            max_tokens=10
        )
        
        if response and "OK" in response.upper():
            print(f"✅ OpenAI funcionando! Resposta: '{response}'")
            return True
        else:
            print(f"⚠️  Resposta inesperada: '{response}'")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao testar OpenAI: {e}")
        return False

def main():
    """Função principal"""
    print("🚀 CONFIGURAÇÃO DO PERSONACREATOR COM OPENAI")
    print("="*50)
    
    # Configura a chave
    if setup_openai_key():
        # Testa a conexão
        if test_openai_connection():
            print("\n🎉 CONFIGURAÇÃO CONCLUÍDA!")
            print("✅ OpenAI configurada e funcionando")
            print("\n🚀 Próximos passos:")
            print("1. Execute: python main.py --channel-url 'URL_DO_CANAL'")
            print("2. O sistema usará gpt-4o-mini automaticamente")
        else:
            print("\n⚠️  Configuração parcial")
            print("   Chave salva, mas teste falhou")
            print("   Verifique se a chave está correta e tem créditos")
    else:
        print("\n💡 Para configurar depois:")
        print("1. Edite o arquivo .env")
        print("2. Adicione: OPENAI_API_KEY=sua_chave")
        print("3. Execute este script novamente")

if __name__ == "__main__":
    main()
