#!/usr/bin/env python3
"""
PersonaCreator - Criação Conversacional de Personas
Sistema inteligente que conversa com você para criar personas personalizadas
"""
import sys
import argparse
from pathlib import Path

# Adiciona o diretório raiz ao path
sys.path.append(str(Path(__file__).parent))

from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown

from database import DatabaseManager
from ai_utils import LLaMAClient
from agents.conversational_orchestrator import ConversationalOrchestrator


def setup_logging():
    """Configura logging mínimo (cada projeto terá seu próprio log)"""
    logger.remove()
    # Log apenas no console para inicialização - SEM CRIAR ARQUIVOS NA RAIZ
    logger.add(
        sys.stderr,
        format="{time:HH:mm:ss} | {level} | {message}",
        level="INFO"
    )


def show_welcome():
    """Mostra boas-vindas"""
    console = Console()
    
    welcome_text = """
# 🎯 PersonaCreator - Modo Conversacional

**Sistema inteligente que conversa com você para criar personas perfeitas!**

## 🚀 Como funciona:

1. **Converso com você** para entender seu nicho específico
2. **Personalizo a análise** baseado nas suas necessidades  
3. **Mostro resultados** em tempo real para seu feedback
4. **Refino a persona** até ficar perfeita para você

## 💡 Vantagens:

- ✅ **Personalização total** para qualquer nicho
- ✅ **Controle completo** de cada etapa
- ✅ **Resultados superiores** aos métodos automáticos
- ✅ **Interface moderna** e intuitiva

**Vamos começar sua jornada de criação de personas! 🚀**
    """
    
    console.print(Panel(
        Markdown(welcome_text),
        title="🤖 Bem-vindo ao PersonaCreator",
        border_style="blue"
    ))


def main():
    """Função principal"""
    parser = argparse.ArgumentParser(
        description="PersonaCreator - Criação conversacional de personas baseada em comentários do YouTube",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:

  # Modo conversacional (recomendado)
  python persona_creator.py
  
  # Com canal específico
  python persona_creator.py --channel-url "https://www.youtube.com/@canal"
  
  # Executar etapa específica (avançado)
  python persona_creator.py --step curation --channel-id 1
  
  # Ver status de um canal
  python persona_creator.py --status --channel-id 1

Para mais informações, consulte README_CHAT.md
        """
    )
    
    parser.add_argument(
        '--channel-url', 
        help='URL do canal do YouTube para análise (opcional - será perguntado se não fornecido)'
    )
    
    parser.add_argument(
        '--step',
        choices=['curation', 'prioritization', 'extraction', 'classification', 'sentiment_analysis', 'persona_generation'],
        help='Executar apenas uma etapa específica (uso avançado)'
    )
    
    parser.add_argument(
        '--channel-id',
        type=int,
        help='ID do canal no banco (necessário para etapas específicas)'
    )
    
    parser.add_argument(
        '--status',
        action='store_true',
        help='Mostra status do workflow para um canal'
    )
    
    parser.add_argument(
        '--no-ai',
        action='store_true',
        help='Desabilita uso de IA (usa métodos simples)'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Modo silencioso (sem boas-vindas)'
    )

    args = parser.parse_args()
    
    # Setup
    setup_logging()
    
    try:
        # Mostra boas-vindas (exceto em modo silencioso)
        if not args.quiet and not args.step and not args.status:
            show_welcome()
        
        # Inicializa componentes
        llama_client = LLaMAClient()

        # Modo conversacional (padrão)
        if not args.step and not args.status:
            chat_orchestrator = ConversationalOrchestrator(llama_client)
            chat_orchestrator.start_conversation(args.channel_url)
            return
        
        # Funcionalidades avançadas (para desenvolvedores)
        from main import PersonaCreator
        orchestrator = PersonaCreator()
        
        if args.status and args.channel_id:
            # Mostra status
            status = orchestrator.get_workflow_status(args.channel_id)
            print(f"\n=== STATUS DO WORKFLOW - Canal {args.channel_id} ===")
            print(f"Vídeos: {status['videos']['total']} total, {status['videos']['relevant']} relevantes")
            print(f"Comentários: {status['comments']['total_comments']}")
            print(f"Comentários com tags: {status['tags']['tagged_comments']}")
            print(f"Comentários analisados: {status['analyses']['analyzed_comments']}")
            print(f"Personas geradas: {status['personas']['personas']}")
            print(f"Workflow completo: {'✓' if status['workflow_complete'] else '✗'}")
            
        elif args.step:
            # Executa etapa específica
            if not args.channel_id and args.step != 'curation':
                print("❌ Erro: --channel-id é obrigatório para etapas específicas (exceto curation)")
                return
                
            result = orchestrator.run_partial_workflow(
                step=args.step,
                channel_id=args.channel_id,
                channel_url=args.channel_url,
                max_videos=20,  # padrão
                selected_tags=['filho', 'filha', 'familia', 'educacao'],  # padrão
                use_ai=not args.no_ai
            )
            
            if result['success']:
                print(f"\n✓ Etapa '{args.step}' concluída com sucesso!")
                logger.info(f"Resultado: {result}")
            else:
                print(f"\n✗ Erro na etapa '{args.step}': {result.get('error', 'Erro desconhecido')}")
                sys.exit(1)
        
        else:
            # Se chegou aqui, faltam argumentos
            print("💡 Dica: Execute sem argumentos para o modo conversacional")
            print("🚀 Iniciando modo conversacional...")
            
            chat_orchestrator = ConversationalOrchestrator(llama_client)
            chat_orchestrator.start_conversation(args.channel_url)
    
    except KeyboardInterrupt:
        console = Console()
        console.print("\n\n👋 Até logo! Obrigado por usar o PersonaCreator!", style="bold blue")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Erro fatal: {e}")
        console = Console()
        console.print(f"\n💥 Erro inesperado: {e}", style="bold red")
        console.print("📋 Logs específicos do projeto estão na pasta do projeto selecionado")
        sys.exit(1)


if __name__ == "__main__":
    main()
