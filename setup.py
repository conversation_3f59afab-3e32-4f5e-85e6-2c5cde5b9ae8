#!/usr/bin/env python3
"""
Script de configuração e instalação do PersonaCreator
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    """Imprime cabeçalho do instalador"""
    print("🎯 PERSONACREATOR - INSTALADOR AUTOMÁTICO")
    print("="*60)
    print("Sistema de análise de personas baseado em comentários do YouTube")
    print("="*60)

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    print("\n🐍 Verificando versão do Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python {version.major}.{version.minor} não é compatível")
        print("   Versão mínima requerida: Python 3.8")
        print("   Baixe em: https://python.org/downloads/")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
    return True

def install_python_dependencies():
    """Instala dependências Python"""
    print("\n📦 Instalando dependências Python...")
    
    try:
        # Atualiza pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("  ✅ pip atualizado")
        
        # Instala dependências
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("  ✅ Dependências instaladas")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"  ❌ Erro na instalação: {e}")
        print("     Tente executar manualmente: pip install -r requirements.txt")
        return False

def check_ollama_installation():
    """Verifica se Ollama está instalado"""
    print("\n🤖 Verificando instalação do Ollama...")
    
    try:
        result = subprocess.run(["ollama", "--version"], 
                              capture_output=True, text=True, check=True)
        print(f"  ✅ Ollama encontrado: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  ❌ Ollama não encontrado")
        return False

def install_ollama():
    """Instala Ollama"""
    print("\n🔧 Instalando Ollama...")
    
    system = platform.system().lower()
    
    if system == "linux" or system == "darwin":  # Linux ou macOS
        try:
            subprocess.run(["curl", "-fsSL", "https://ollama.ai/install.sh"], 
                          stdout=subprocess.PIPE, check=True)
            subprocess.run(["sh"], input=subprocess.PIPE, check=True)
            print("  ✅ Ollama instalado")
            return True
        except subprocess.CalledProcessError:
            print("  ❌ Erro na instalação do Ollama")
            print("     Instale manualmente: https://ollama.ai/")
            return False
    
    elif system == "windows":
        print("  ⚠️  Instalação automática não disponível no Windows")
        print("     Baixe e instale manualmente: https://ollama.ai/download/windows")
        return False
    
    else:
        print(f"  ⚠️  Sistema {system} não suportado para instalação automática")
        print("     Visite: https://ollama.ai/")
        return False

def download_llama_model():
    """Baixa modelo LLaMA 3"""
    print("\n📥 Baixando modelo LLaMA 3 8B...")
    
    try:
        # Verifica se Ollama está rodando
        subprocess.run(["ollama", "list"], capture_output=True, check=True)
        
        # Baixa o modelo
        print("  🔄 Baixando modelo (isso pode demorar alguns minutos)...")
        result = subprocess.run(["ollama", "pull", "llama3:8b-instruct"], 
                              capture_output=True, text=True, check=True)
        print("  ✅ Modelo LLaMA 3 8B baixado")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"  ❌ Erro ao baixar modelo: {e}")
        print("     Certifique-se de que o Ollama está rodando: ollama serve")
        print("     Ou baixe manualmente: ollama pull llama3:8b-instruct")
        return False

def create_directories():
    """Cria diretórios necessários"""
    print("\n📁 Criando diretórios...")

    # Removido "logs" - agora cada projeto tem seu próprio log
    directories = ["data", "output"]

    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"  ✅ {directory}/")

    print("  ℹ️  logs/ - cada projeto terá seu próprio diretório de logs")
    return True

def create_env_file():
    """Cria arquivo .env se não existir"""
    print("\n⚙️  Configurando arquivo de ambiente...")
    
    env_file = Path(".env")
    
    if env_file.exists():
        print("  ✅ Arquivo .env já existe")
        return True
    
    # Copia do exemplo
    example_file = Path(".env.example")
    if example_file.exists():
        env_file.write_text(example_file.read_text())
        print("  ✅ Arquivo .env criado a partir do exemplo")
    else:
        # Cria arquivo básico
        env_content = """# Configurações do PersonaCreator
LLAMA_API_URL=http://localhost:11434
MAX_VIDEOS_PER_CHANNEL=100
MAX_COMMENTS_PER_VIDEO=500
LOG_LEVEL=INFO
"""
        env_file.write_text(env_content)
        print("  ✅ Arquivo .env criado")
    
    return True

def run_tests():
    """Executa testes do sistema"""
    print("\n🧪 Executando testes do sistema...")
    
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True, check=True)
        print("  ✅ Todos os testes passaram!")
        return True
    except subprocess.CalledProcessError:
        print("  ⚠️  Alguns testes falharam")
        print("     Execute 'python test_system.py' para detalhes")
        return False

def print_final_instructions():
    """Imprime instruções finais"""
    print("\n🎉 INSTALAÇÃO CONCLUÍDA!")
    print("="*60)
    print("\n📋 PRÓXIMOS PASSOS:")
    print("\n1. Inicie o Ollama (se não estiver rodando):")
    print("   ollama serve")
    print("\n2. Execute o teste do sistema:")
    print("   python test_system.py")
    print("\n3. Execute um exemplo:")
    print("   python exemplo_uso.py")
    print("\n4. Ou use diretamente:")
    print('   python main.py --channel-url "https://www.youtube.com/@seucanal"')
    print("\n📚 DOCUMENTAÇÃO:")
    print("   • README.md - Guia completo")
    print("   • exemplo_uso.py - Exemplos práticos")
    print("   • config.py - Configurações personalizáveis")
    print("\n🆘 SUPORTE:")
    print("   • Logs em: logs/persona_creator.log")
    print("   • Teste sistema: python test_system.py")

def main():
    """Função principal do instalador"""
    print_header()
    
    # Lista de verificações
    checks = [
        ("Versão do Python", check_python_version),
        ("Dependências Python", install_python_dependencies),
        ("Diretórios", create_directories),
        ("Arquivo de configuração", create_env_file),
    ]
    
    # Verificações opcionais do Ollama
    ollama_checks = [
        ("Ollama instalado", check_ollama_installation),
        ("Modelo LLaMA", download_llama_model),
        ("Testes do sistema", run_tests),
    ]
    
    # Executa verificações básicas
    print("\n🔍 VERIFICAÇÕES BÁSICAS")
    print("-" * 30)
    
    basic_success = True
    for name, check_func in checks:
        print(f"\n{name}...")
        if not check_func():
            basic_success = False
    
    if not basic_success:
        print("\n❌ Instalação básica falhou!")
        print("   Corrija os erros acima antes de continuar.")
        sys.exit(1)
    
    # Verificações do Ollama
    print("\n\n🤖 CONFIGURAÇÃO DO OLLAMA")
    print("-" * 30)
    
    ollama_installed = check_ollama_installation()
    
    if not ollama_installed:
        install_ollama_choice = input("\n❓ Deseja tentar instalar o Ollama automaticamente? (s/N): ").lower()
        if install_ollama_choice in ['s', 'sim', 'y', 'yes']:
            ollama_installed = install_ollama()
    
    if ollama_installed:
        # Tenta baixar modelo
        model_choice = input("\n❓ Deseja baixar o modelo LLaMA 3 8B agora? (s/N): ").lower()
        if model_choice in ['s', 'sim', 'y', 'yes']:
            download_llama_model()
        
        # Executa testes
        test_choice = input("\n❓ Deseja executar os testes do sistema? (S/n): ").lower()
        if test_choice not in ['n', 'no', 'não']:
            run_tests()
    
    # Instruções finais
    print_final_instructions()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Instalação interrompida pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Erro durante a instalação: {e}")
        sys.exit(1)
