"""
Agente 4 - Classificador de Comentários
Responsável por aplicar tags aos comentários baseado em palavras-chave e contexto
"""
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Set
from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from crewai import Agent, Task

from database import DatabaseManager
from ai_utils import LLaMAClient, AnalysisPrompts
from config import COMMENT_TAGS


class CommentClassifierAgent:
    """Agente classificador de comentários com tags"""
    
    def __init__(self, db_manager: DatabaseManager, llama_client: LLaMAClient):
        self.db = db_manager
        self.llama = llama_client
        self.comment_tags = COMMENT_TAGS
    
    def extract_keywords_from_text(self, text: str) -> Set[str]:
        """Extrai palavras-chave normalizadas do texto"""
        # Remove pontuação e converte para minúsculas
        clean_text = re.sub(r'[^\w\s]', ' ', text.lower())
        words = set(clean_text.split())
        
        # Remove palavras muito curtas
        words = {word for word in words if len(word) > 2}
        
        return words
    
    def classify_comment_simple(self, comment_text: str) -> List[Dict[str, Any]]:
        """Classificação simples baseada em palavras-chave"""
        text_words = self.extract_keywords_from_text(comment_text)
        found_tags = []
        
        for tag_name, keywords in self.comment_tags.items():
            matched_keywords = []
            confidence = 0.0
            
            for keyword in keywords:
                keyword_words = set(keyword.lower().split())
                
                # Verifica se todas as palavras da keyword estão no texto
                if keyword_words.issubset(text_words):
                    matched_keywords.append(keyword)
                    confidence += 1.0
                # Verifica se pelo menos uma palavra da keyword está no texto
                elif keyword_words.intersection(text_words):
                    matched_keywords.append(keyword)
                    confidence += 0.5
            
            if matched_keywords:
                # Normaliza confiança baseada no número de keywords da tag
                normalized_confidence = min(confidence / len(keywords), 1.0)
                
                found_tags.append({
                    'tag': tag_name,
                    'confidence': normalized_confidence,
                    'matched_keywords': matched_keywords
                })
        
        # Ordena por confiança
        found_tags.sort(key=lambda x: x['confidence'], reverse=True)
        return found_tags
    
    def classify_comment_with_ai(self, comment_text: str) -> List[Dict[str, Any]]:
        """Classificação avançada usando IA"""
        prompt = AnalysisPrompts.classify_comment_tags(comment_text, self.comment_tags)
        response = self.llama.generate_response(prompt, temperature=0.3)

        try:
            # Tenta extrair JSON da resposta
            ai_result = self._extract_json_from_response(response)
            if ai_result and 'tags_encontradas' in ai_result:
                return ai_result['tags_encontradas']
        except Exception as e:
            logger.warning(f"Erro na classificação com IA: {e}")

        # Fallback para classificação simples
        logger.debug(f"Usando classificação simples para: {comment_text[:50]}...")
        return self.classify_comment_simple(comment_text)

    def _extract_json_from_response(self, response: str) -> Optional[Dict]:
        """Tenta extrair JSON de uma resposta que pode conter texto extra"""
        if not response:
            return None

        # Tenta JSON direto primeiro
        try:
            return json.loads(response.strip())
        except:
            pass

        # Procura por JSON entre chaves
        import re
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except:
                continue

        # Procura por linhas que começam com {
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('{') and line.endswith('}'):
                try:
                    return json.loads(line)
                except:
                    continue

        return None

    def classify_single_comment(self, comment: Dict[str, Any], use_ai: bool = True) -> List[Dict[str, Any]]:
        """Classifica um único comentário"""
        comment_text = comment.get('text', '')
        
        if not comment_text:
            return []
        
        # Usa IA se disponível, senão usa classificação simples
        if use_ai:
            tags = self.classify_comment_with_ai(comment_text)
        else:
            tags = self.classify_comment_simple(comment_text)
        
        # Filtra tags com confiança muito baixa
        filtered_tags = [tag for tag in tags if tag.get('confianca', tag.get('confidence', 0)) > 0.3]
        
        return filtered_tags
    
    def save_comment_tags(self, comment_id: int, tags: List[Dict[str, Any]]):
        """Salva tags de um comentário no banco de dados"""
        for tag_data in tags:
            tag_name = tag_data.get('tag', '')
            confidence = tag_data.get('confianca', tag_data.get('confidence', 0))
            
            if tag_name and confidence > 0:
                self.db.insert_comment_tag(
                    comment_id=comment_id,
                    tag_name=tag_name,
                    tag_category=tag_name,  # Por enquanto, categoria = nome da tag
                    confidence_score=confidence
                )
    
    def classify_channel_comments(self, channel_id: int, use_ai: bool = True, batch_size: int = 100) -> Dict[str, Any]:
        """Classifica todos os comentários de um canal"""
        logger.info(f"Classificando comentários do canal {channel_id}")
        
        # Busca comentários não classificados
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT c.id, c.text, c.author, c.like_count, v.title as video_title
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                LEFT JOIN comment_tags ct ON c.id = ct.comment_id
                WHERE v.channel_id = ? AND ct.id IS NULL
                ORDER BY c.like_count DESC, LENGTH(c.text) DESC
            """, (channel_id,))
            
            unclassified_comments = [dict(row) for row in cursor.fetchall()]
        
        if not unclassified_comments:
            logger.info("Nenhum comentário não classificado encontrado")
            return {'success': True, 'message': 'Todos os comentários já foram classificados'}
        
        logger.info(f"Encontrados {len(unclassified_comments)} comentários para classificar")
        
        classification_stats = {
            'total_processed': 0,
            'total_tags_applied': 0,
            'tag_distribution': {},
            'comments_with_tags': 0
        }
        
        # Processa comentários em lotes
        for i in range(0, len(unclassified_comments), batch_size):
            batch = unclassified_comments[i:i + batch_size]
            logger.info(f"Processando lote {i//batch_size + 1}/{(len(unclassified_comments) + batch_size - 1)//batch_size}")
            
            for comment in batch:
                try:
                    # Classifica comentário
                    tags = self.classify_single_comment(comment, use_ai=use_ai)
                    
                    if tags:
                        # Salva tags no banco
                        self.save_comment_tags(comment['id'], tags)
                        
                        # Atualiza estatísticas
                        classification_stats['comments_with_tags'] += 1
                        classification_stats['total_tags_applied'] += len(tags)
                        
                        for tag_data in tags:
                            tag_name = tag_data.get('tag', '')
                            if tag_name:
                                classification_stats['tag_distribution'][tag_name] = \
                                    classification_stats['tag_distribution'].get(tag_name, 0) + 1
                    
                    classification_stats['total_processed'] += 1
                    
                except Exception as e:
                    logger.warning(f"Erro ao classificar comentário {comment['id']}: {e}")
        
        result = {
            'success': True,
            'channel_id': channel_id,
            'classification_stats': classification_stats,
            'coverage_rate': classification_stats['comments_with_tags'] / classification_stats['total_processed'] if classification_stats['total_processed'] > 0 else 0
        }
        
        logger.info(f"Classificação concluída: {classification_stats['comments_with_tags']}/{classification_stats['total_processed']} comentários classificados")
        return result
    
    def get_comments_by_tags(self, channel_id: int, selected_tags: List[str], min_confidence: float = 0.5) -> List[Dict[str, Any]]:
        """Busca comentários que possuem tags específicas"""
        with self.db.get_connection() as conn:
            placeholders = ','.join(['?' for _ in selected_tags])
            
            cursor = conn.execute(f"""
                SELECT DISTINCT 
                    c.id,
                    c.text,
                    c.author,
                    c.like_count,
                    c.reply_count,
                    v.title as video_title,
                    v.url as video_url,
                    GROUP_CONCAT(ct.tag_name || ':' || ct.confidence_score) as tags_info
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                JOIN comment_tags ct ON c.id = ct.comment_id
                WHERE v.channel_id = ? 
                AND ct.tag_name IN ({placeholders})
                AND ct.confidence_score >= ?
                GROUP BY c.id
                ORDER BY c.like_count DESC, LENGTH(c.text) DESC
            """, [channel_id] + selected_tags + [min_confidence])
            
            comments = []
            for row in cursor.fetchall():
                comment_dict = dict(row)
                
                # Processa informações das tags
                tags_info = []
                if comment_dict['tags_info']:
                    for tag_info in comment_dict['tags_info'].split(','):
                        if ':' in tag_info:
                            tag_name, confidence = tag_info.split(':', 1)
                            tags_info.append({
                                'tag': tag_name,
                                'confidence': float(confidence)
                            })
                
                comment_dict['tags'] = tags_info
                del comment_dict['tags_info']
                comments.append(comment_dict)
        
        logger.info(f"Encontrados {len(comments)} comentários com tags: {', '.join(selected_tags)}")
        return comments
    
    def export_classification_report(self, channel_id: int, output_file: str = None) -> str:
        """Exporta relatório de classificação"""
        if not output_file:
            output_file = OUTPUT_DIR / "classification_report.json"
        
        # Estatísticas gerais
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT 
                    ct.tag_name,
                    COUNT(*) as count,
                    AVG(ct.confidence_score) as avg_confidence,
                    MIN(ct.confidence_score) as min_confidence,
                    MAX(ct.confidence_score) as max_confidence
                FROM comment_tags ct
                JOIN comments c ON ct.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
                GROUP BY ct.tag_name
                ORDER BY count DESC
            """, (channel_id,))
            
            tag_stats = [dict(row) for row in cursor.fetchall()]
        
        # Comentários mais representativos por tag
        tag_examples = {}
        for tag_stat in tag_stats[:10]:  # Top 10 tags
            tag_name = tag_stat['tag_name']
            
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT c.text, c.author, c.like_count, ct.confidence_score
                    FROM comments c
                    JOIN comment_tags ct ON c.id = ct.comment_id
                    JOIN videos v ON c.video_id = v.id
                    WHERE v.channel_id = ? AND ct.tag_name = ?
                    ORDER BY ct.confidence_score DESC, c.like_count DESC
                    LIMIT 5
                """, (channel_id, tag_name))
                
                tag_examples[tag_name] = [dict(row) for row in cursor.fetchall()]
        
        export_data = {
            'channel_id': channel_id,
            'classification_date': str(datetime.now()),
            'tag_statistics': tag_stats,
            'tag_examples': tag_examples,
            'total_unique_tags': len(tag_stats),
            'total_tag_applications': sum(stat['count'] for stat in tag_stats)
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Relatório de classificação exportado para: {output_file}")
        return str(output_file)
    
    def run_classification_workflow(self, channel_id: int, use_ai: bool = True) -> Dict[str, Any]:
        """Executa workflow completo de classificação"""
        logger.info("Iniciando workflow de classificação de comentários")
        
        # 1. Classifica comentários
        result = self.classify_channel_comments(channel_id, use_ai=use_ai)
        
        if not result['success']:
            return result
        
        # 2. Exporta relatório
        export_file = self.export_classification_report(channel_id)
        result['export_file'] = export_file
        
        logger.info("Workflow de classificação concluído")
        return result


def create_comment_classifier_crew_agent() -> Agent:
    """Cria agente CrewAI para classificação de comentários"""
    return Agent(
        role='Classificador de Comentários',
        goal='Aplicar tags contextuais aos comentários para facilitar análise posterior',
        backstory="""Você é um especialista em análise de texto e classificação de conteúdo 
        sobre parentalidade. Sua expertise está em identificar padrões nos comentários e 
        aplicar tags relevantes que facilitem a análise posterior de dores, desejos e 
        oportunidades relacionadas à experiência parental.""",
        verbose=True,
        allow_delegation=False
    )


def create_classification_task(agent: Agent, channel_id: int) -> Task:
    """Cria task CrewAI para classificação"""
    return Task(
        description=f"""
        Classifique todos os comentários extraídos do canal ID: {channel_id}
        
        Suas responsabilidades:
        1. Analisar cada comentário para identificar contexto e temas
        2. Aplicar tags relevantes baseadas em palavras-chave e contexto
        3. Calcular scores de confiança para cada tag aplicada
        4. Organizar comentários por categorias para análise posterior
        5. Gerar relatório de distribuição de tags
        
        Tags principais a aplicar:
        - filho/filha: comentários sobre filhos específicos
        - família: dinâmicas familiares gerais
        - educação: questões educacionais e escolares
        - comportamento: disciplina e comportamento infantil
        - saúde: questões de saúde e bem-estar
        - desenvolvimento: crescimento e marcos de desenvolvimento
        
        Priorize precisão sobre quantidade - é melhor aplicar poucas tags com alta confiança.
        """,
        agent=agent,
        expected_output="Base de comentários classificados com tags e scores de confiança"
    )
