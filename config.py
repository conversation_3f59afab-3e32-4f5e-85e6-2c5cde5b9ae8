"""
Configurações do projeto PersonaCreator
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

# Diretórios do projeto
PROJECT_ROOT = Path(__file__).parent
PROJECTS_DIR = PROJECT_ROOT / "projects"

# Cria diretório de projetos se não existir
PROJECTS_DIR.mkdir(exist_ok=True)

# Configurações do banco de dados (agora cada projeto tem o seu)
# DATABASE_PATH removido - cada projeto tem seu próprio banco

# Configurações da IA
# Prioridade: OpenAI > Ollama local
USE_OPENAI = os.getenv("USE_OPENAI", "true").lower() == "true"
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-4o-mini")

# Configurações Ollama (fallback)
LLAMA_MODEL_NAME = os.getenv("LLAMA_MODEL_NAME", "gemma3:4b")
LLAMA_API_URL = os.getenv("LLAMA_API_URL", "http://localhost:11434")

# Configurações específicas por modelo
MODEL_CONFIGS = {
    # OpenAI Models
    "gpt-4o-mini": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "context_window": 128000,
        "has_thinking": False,
        "provider": "openai"
    },
    "gpt-4o": {
        "temperature": 0.7,
        "max_tokens": 4000,
        "context_window": 128000,
        "has_thinking": False,
        "provider": "openai"
    },
    # Ollama Models (fallback)
    "deepseek-r1:8b": {
        "temperature": 0.7,
        "max_tokens": 2000,
        "context_window": 8192,
        "has_thinking": True,
        "provider": "ollama"
    },
    "gemma3:4b": {
        "temperature": 0.8,
        "max_tokens": 1500,
        "context_window": 4096,
        "has_thinking": False,
        "provider": "ollama"
    },
    "gemma3:12b": {
        "temperature": 0.7,
        "max_tokens": 2500,
        "context_window": 8192,
        "has_thinking": False,
        "provider": "ollama"
    }
}

# Configurações do YouTube
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY")
MAX_VIDEOS_PER_CHANNEL = int(os.getenv("MAX_VIDEOS_PER_CHANNEL", "100"))
MAX_COMMENTS_PER_VIDEO = int(os.getenv("MAX_COMMENTS_PER_VIDEO", "500"))

# Configurações para canais grandes
LARGE_CHANNEL_THRESHOLD = 100  # Considera "grande" se tiver mais de 100 vídeos
LARGE_CHANNEL_SAMPLE_SIZE = 30  # Para canais grandes, analisa apenas 30 vídeos
LARGE_CHANNEL_TIME_FILTER_DAYS = 365  # Últimos 365 dias para canais grandes

# Configurações de análise
TARGET_CONTEXT_KEYWORDS = [
    "pais", "filhos", "família", "criança", "educação", "parentalidade",
    "mãe", "pai", "bebê", "adolescente", "desenvolvimento"
]

COMMENT_TAGS = {
    "filho": ["meu filho", "filho", "filhinho", "garoto", "menino"],
    "filha": ["minha filha", "filha", "filhinha", "garota", "menina"],
    "familia": ["família", "casa", "lar", "parentes"],
    "educacao": ["escola", "estudo", "aprender", "ensinar", "educação"],
    "comportamento": ["comportamento", "disciplina", "birra", "obediência"],
    "saude": ["saúde", "médico", "doença", "cuidado", "bem-estar"],
    "desenvolvimento": ["crescimento", "desenvolvimento", "idade", "fase"]
}

# Configurações de logging
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# DEPRECATED: Logs agora são por projeto, não globais
# Mantido apenas para compatibilidade com main.py legado
LOGS_DIR = PROJECT_ROOT / "logs"

# Configurações de análise de sentimentos e extração
ANALYSIS_CATEGORIES = {
    "dores": [
        "problema", "dificuldade", "preocupação", "medo", "ansiedade",
        "estresse", "cansaço", "frustração", "raiva", "tristeza"
    ],
    "desejos": [
        "quero", "desejo", "sonho", "espero", "gostaria", "almejo",
        "pretendo", "planejo", "objetivo", "meta"
    ],
    "sonhos": [
        "sonho", "futuro", "esperança", "ambição", "ideal", "visão",
        "aspiração", "projeto de vida"
    ],
    "frustracoes": [
        "frustração", "decepção", "falha", "erro", "arrependimento",
        "insatisfação", "descontentamento"
    ],
    "palavras_magicas": [
        "incrível", "fantástico", "maravilhoso", "perfeito", "excelente",
        "ótimo", "adorei", "amei", "recomendo", "vale a pena"
    ],
    "oportunidades": [
        "oportunidade", "chance", "possibilidade", "negócio", "investimento",
        "mercado", "demanda", "necessidade", "solução"
    ]
}
