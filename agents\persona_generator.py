"""
Agente 6 - G<PERSON>dor de Persona
Responsável por gerar persona detalhada baseada em todas as análises anteriores
"""
import json
import statistics
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from crewai import Agent, Task

from database import DatabaseManager
from ai_utils import LLaMAClient, AnalysisPrompts
# OUTPUT_DIR removido - cada projeto tem sua estrutura


class PersonaGeneratorAgent:
    """Agente gerador de personas baseado em análises de comentários"""
    
    def __init__(self, db_manager: DatabaseManager, llama_client: LLaMAClient):
        self.db = db_manager
        self.llama = llama_client
    
    def aggregate_analysis_data(self, channel_id: int) -> Dict[str, Any]:
        """Agrega todos os dados de análise para geração da persona"""
        logger.info(f"Agregando dados de análise do canal {channel_id}")
        
        # Busca resumo completo das análises
        analysis_summary = self.db.get_analysis_summary(channel_id)
        
        # Estatísticas dos comentários
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT 
                    COUNT(DISTINCT c.id) as total_comments,
                    COUNT(DISTINCT c.author) as unique_authors,
                    AVG(c.like_count) as avg_likes,
                    AVG(LENGTH(c.text)) as avg_comment_length,
                    COUNT(DISTINCT v.id) as videos_analyzed
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
            """, (channel_id,))
            
            comment_stats = dict(cursor.fetchone())
        
        # Top tags mais frequentes
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT ct.tag_name, COUNT(*) as frequency, AVG(ct.confidence_score) as avg_confidence
                FROM comment_tags ct
                JOIN comments c ON ct.comment_id = c.id
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
                GROUP BY ct.tag_name
                ORDER BY frequency DESC
                LIMIT 10
            """, (channel_id,))
            
            top_tags = [dict(row) for row in cursor.fetchall()]
        
        # Padrões demográficos inferidos
        demographic_patterns = self._infer_demographic_patterns(channel_id)
        
        aggregated_data = {
            'channel_id': channel_id,
            'comment_statistics': comment_stats,
            'analysis_summary': analysis_summary,
            'top_tags': top_tags,
            'demographic_patterns': demographic_patterns,
            'aggregation_date': str(datetime.now())
        }
        
        logger.info("Dados agregados com sucesso")
        return aggregated_data
    
    def _infer_demographic_patterns(self, channel_id: int) -> Dict[str, Any]:
        """Infere padrões demográficos baseado nos comentários"""
        patterns = {
            'age_indicators': {},
            'family_structure': {},
            'concerns_by_age': {},
            'language_patterns': []
        }
        
        # Busca indicadores de idade dos filhos
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT c.text
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
                AND (c.text LIKE '%anos%' OR c.text LIKE '%meses%' OR c.text LIKE '%idade%')
            """, (channel_id,))
            
            age_comments = [row[0] for row in cursor.fetchall()]
        
        # Analisa padrões de idade
        age_mentions = {
            'bebê': 0, '0-2 anos': 0, '3-5 anos': 0, 
            '6-10 anos': 0, '11-15 anos': 0, '16+ anos': 0
        }
        
        for comment in age_comments[:100]:  # Limita para performance
            text_lower = comment.lower()
            
            if any(word in text_lower for word in ['bebê', 'recém-nascido', 'meses']):
                age_mentions['bebê'] += 1
            elif any(word in text_lower for word in ['2 anos', '1 ano', 'pequeno']):
                age_mentions['0-2 anos'] += 1
            elif any(word in text_lower for word in ['3 anos', '4 anos', '5 anos', 'pré-escola']):
                age_mentions['3-5 anos'] += 1
            elif any(word in text_lower for word in ['escola', 'fundamental', '6 anos', '7 anos', '8 anos', '9 anos', '10 anos']):
                age_mentions['6-10 anos'] += 1
            elif any(word in text_lower for word in ['adolescente', '11 anos', '12 anos', '13 anos', '14 anos', '15 anos']):
                age_mentions['11-15 anos'] += 1
            elif any(word in text_lower for word in ['jovem', '16 anos', '17 anos', '18 anos']):
                age_mentions['16+ anos'] += 1
        
        patterns['age_indicators'] = age_mentions
        
        # Analisa estrutura familiar
        family_indicators = {
            'mãe_solteira': 0, 'pai_solteiro': 0, 'casal': 0, 
            'família_grande': 0, 'filho_único': 0
        }
        
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT c.text
                FROM comments c
                JOIN videos v ON c.video_id = v.id
                WHERE v.channel_id = ?
                AND (c.text LIKE '%solteira%' OR c.text LIKE '%marido%' OR c.text LIKE '%esposa%' 
                     OR c.text LIKE '%filhos%' OR c.text LIKE '%único%')
                LIMIT 200
            """, (channel_id,))
            
            family_comments = [row[0] for row in cursor.fetchall()]
        
        for comment in family_comments:
            text_lower = comment.lower()
            
            if 'mãe solteira' in text_lower or 'sozinha' in text_lower:
                family_indicators['mãe_solteira'] += 1
            elif 'pai solteiro' in text_lower:
                family_indicators['pai_solteiro'] += 1
            elif any(word in text_lower for word in ['marido', 'esposa', 'casal', 'juntos']):
                family_indicators['casal'] += 1
            elif any(word in text_lower for word in ['3 filhos', '4 filhos', 'muitos filhos']):
                family_indicators['família_grande'] += 1
            elif 'filho único' in text_lower or 'só um filho' in text_lower:
                family_indicators['filho_único'] += 1
        
        patterns['family_structure'] = family_indicators
        
        return patterns
    
    def generate_persona_with_ai(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera persona usando IA baseada nos dados agregados"""
        prompt = AnalysisPrompts.generate_persona(aggregated_data['analysis_summary'])
        response = self.llama.generate_response(prompt, temperature=0.4, max_tokens=2000)
        
        try:
            # Tenta extrair JSON da resposta
            persona_data = self._extract_json_from_response(response)
            if persona_data and 'nome_persona' in persona_data:
                # Adiciona dados estatísticos
                persona_data['data_source'] = {
                    'total_comments_analyzed': aggregated_data['comment_statistics']['total_comments'],
                    'unique_authors': aggregated_data['comment_statistics']['unique_authors'],
                    'videos_analyzed': aggregated_data['comment_statistics']['videos_analyzed'],
                    'top_tags': aggregated_data['top_tags'][:5],
                    'demographic_patterns': aggregated_data['demographic_patterns']
                }
                return persona_data

        except Exception as e:
            logger.error(f"Erro ao gerar persona com IA: {e}")

        # Fallback para método simples
        logger.debug("Usando geração de persona simples como fallback")
        return self._generate_persona_fallback(aggregated_data)

    def _extract_json_from_response(self, response: str) -> Optional[Dict]:
        """Tenta extrair JSON de uma resposta que pode conter texto extra"""
        if not response:
            return None

        # Tenta JSON direto primeiro
        try:
            return json.loads(response.strip())
        except:
            pass

        # Procura por JSON entre chaves
        import re
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except:
                continue

        # Procura por linhas que começam com {
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('{') and line.endswith('}'):
                try:
                    return json.loads(line)
                except:
                    continue

        return None

    def _generate_persona_fallback(self, aggregated_data: Dict[str, Any]) -> Dict[str, Any]:
        """Gera persona usando método de fallback baseado em dados estatísticos"""
        analysis_summary = aggregated_data['analysis_summary']
        demographic_patterns = aggregated_data['demographic_patterns']
        
        # Determina faixa etária predominante dos filhos
        age_indicators = demographic_patterns.get('age_indicators', {})
        predominant_age = max(age_indicators.items(), key=lambda x: x[1])[0] if age_indicators else '3-5 anos'
        
        # Determina estrutura familiar predominante
        family_structure = demographic_patterns.get('family_structure', {})
        predominant_family = max(family_structure.items(), key=lambda x: x[1])[0] if family_structure else 'casal'
        
        # Extrai top dores
        top_pains = []
        if 'dores' in analysis_summary:
            pain_contents = [item['content'] for item in analysis_summary['dores'][:5]]
            top_pains = [content.split(';')[0].strip() for content in pain_contents if content]
        
        # Extrai top desejos
        top_desires = []
        if 'desejos_sonhos' in analysis_summary:
            desire_contents = [item['content'] for item in analysis_summary['desejos_sonhos'][:5]]
            for content in desire_contents:
                if 'Desejos:' in content:
                    desires_part = content.split('Desejos:')[1].split('.')[0]
                    top_desires.append(desires_part.strip())
        
        # Extrai palavras mágicas
        magic_words = []
        if 'palavras_magicas_oportunidades' in analysis_summary:
            magic_contents = [item['content'] for item in analysis_summary['palavras_magicas_oportunidades'][:3]]
            for content in magic_contents:
                if 'Palavras mágicas:' in content:
                    magic_part = content.split('Palavras mágicas:')[1].split('.')[0]
                    magic_words.extend([word.strip() for word in magic_part.split(';')])
        
        persona_data = {
            'nome_persona': 'Maria Educadora',
            'demografia': {
                'idade': '32-38 anos',
                'localização': 'Região Sudeste, Brasil',
                'situação_familiar': predominant_family,
                'filhos': predominant_age,
                'renda': 'Classe média (R$ 3.000-8.000)'
            },
            'dores_principais': top_pains[:5],
            'desejos_principais': top_desires[:5],
            'sonhos_aspiracoes': [
                'Ver os filhos bem-sucedidos',
                'Proporcionar boa educação',
                'Família unida e feliz'
            ],
            'frustracoes_comuns': [
                'Falta de tempo para os filhos',
                'Dificuldades financeiras',
                'Pressão social sobre parentalidade'
            ],
            'palavras_magicas': magic_words[:10],
            'oportunidades_negocio': [
                'Consultoria em educação parental',
                'Produtos para organização familiar',
                'Cursos online para pais'
            ],
            'canais_comunicacao': ['YouTube', 'Instagram', 'WhatsApp', 'Facebook'],
            'tom_comunicacao': 'Empático, prático, sem julgamentos',
            'descricao_narrativa': f"""
            Maria é uma mãe de {predominant_age.replace('-', ' a ')} que busca constantemente 
            informações sobre como ser uma melhor mãe. Ela se preocupa com o desenvolvimento 
            dos filhos e frequentemente questiona se está fazendo as escolhas certas. 
            
            Ativa em redes sociais, especialmente YouTube, onde busca dicas práticas e 
            experiências de outras mães. Valoriza conteúdo autêntico e se identifica com 
            histórias reais de outros pais.
            """,
            'score_confianca': 0.75,
            'data_source': aggregated_data.get('comment_statistics', {})
        }
        
        return persona_data
    
    def save_persona_to_database(self, channel_id: int, persona_data: Dict[str, Any]) -> int:
        """Salva persona gerada no banco de dados"""
        persona_db_data = {
            'channel_id': channel_id,
            'persona_name': persona_data.get('nome_persona', 'Persona Gerada'),
            'demographic_data': persona_data.get('demografia', {}),
            'pain_points': persona_data.get('dores_principais', []),
            'desires': persona_data.get('desejos_principais', []),
            'dreams': persona_data.get('sonhos_aspiracoes', []),
            'frustrations': persona_data.get('frustracoes_comuns', []),
            'magic_words': persona_data.get('palavras_magicas', []),
            'business_opportunities': persona_data.get('oportunidades_negocio', []),
            'persona_description': persona_data.get('descricao_narrativa', ''),
            'confidence_score': persona_data.get('score_confianca', 0.0)
        }
        
        persona_id = self.db.insert_persona(persona_db_data)
        logger.info(f"Persona salva no banco de dados com ID: {persona_id}")
        return persona_id
    
    def export_persona_report(self, persona_data: Dict[str, Any], output_file: str = None) -> str:
        """Exporta relatório completo da persona"""
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"persona_report_{timestamp}.json"
        
        # Adiciona metadados do relatório
        report_data = {
            'report_metadata': {
                'generation_date': str(datetime.now()),
                'report_version': '1.0',
                'confidence_level': persona_data.get('score_confianca', 0.0)
            },
            'persona': persona_data
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        # Também cria versão em markdown para leitura
        markdown_file = str(output_file).replace('.json', '.md')
        self._create_markdown_report(persona_data, markdown_file)
        
        logger.info(f"Relatório da persona exportado para: {output_file}")
        return str(output_file)
    
    def _create_markdown_report(self, persona_data: Dict[str, Any], markdown_file: str):
        """Cria relatório em formato Markdown"""
        markdown_content = f"""# Relatório de Persona: {persona_data.get('nome_persona', 'Persona')}

## Dados Demográficos
- **Idade**: {persona_data.get('demografia', {}).get('idade', 'N/A')}
- **Localização**: {persona_data.get('demografia', {}).get('localização', 'N/A')}
- **Situação Familiar**: {persona_data.get('demografia', {}).get('situação_familiar', 'N/A')}
- **Filhos**: {persona_data.get('demografia', {}).get('filhos', 'N/A')}
- **Renda**: {persona_data.get('demografia', {}).get('renda', 'N/A')}

## Principais Dores
"""
        
        for i, pain in enumerate(persona_data.get('dores_principais', []), 1):
            markdown_content += f"{i}. {pain}\n"
        
        markdown_content += "\n## Principais Desejos\n"
        for i, desire in enumerate(persona_data.get('desejos_principais', []), 1):
            markdown_content += f"{i}. {desire}\n"
        
        markdown_content += "\n## Sonhos e Aspirações\n"
        for i, dream in enumerate(persona_data.get('sonhos_aspiracoes', []), 1):
            markdown_content += f"{i}. {dream}\n"
        
        markdown_content += "\n## Palavras Mágicas\n"
        magic_words = persona_data.get('palavras_magicas', [])
        if magic_words:
            markdown_content += ", ".join(magic_words) + "\n"
        
        markdown_content += "\n## Oportunidades de Negócio\n"
        for i, opportunity in enumerate(persona_data.get('oportunidades_negocio', []), 1):
            markdown_content += f"{i}. {opportunity}\n"
        
        markdown_content += f"\n## Descrição Narrativa\n{persona_data.get('descricao_narrativa', '')}\n"
        
        markdown_content += f"\n## Confiança da Análise\n{persona_data.get('score_confianca', 0.0):.2%}\n"
        
        with open(markdown_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
    
    def generate_persona(self, channel_id: int) -> Dict[str, Any]:
        """Gera persona completa para um canal"""
        logger.info(f"Gerando persona para canal {channel_id}")
        
        # 1. Agrega dados de análise
        aggregated_data = self.aggregate_analysis_data(channel_id)
        
        # Verifica se há dados suficientes
        if not aggregated_data['analysis_summary']:
            return {
                'success': False, 
                'error': 'Dados insuficientes para gerar persona. Execute as análises anteriores primeiro.'
            }
        
        # 2. Gera persona com IA
        persona_data = self.generate_persona_with_ai(aggregated_data)
        
        # 3. Salva no banco de dados
        persona_id = self.save_persona_to_database(channel_id, persona_data)
        
        # 4. Exporta relatório
        report_file = self.export_persona_report(persona_data)
        
        result = {
            'success': True,
            'channel_id': channel_id,
            'persona_id': persona_id,
            'persona_data': persona_data,
            'report_file': report_file,
            'confidence_score': persona_data.get('score_confianca', 0.0)
        }
        
        logger.info(f"Persona gerada com sucesso (ID: {persona_id})")
        return result
    
    def run_persona_generation_workflow(self, channel_id: int) -> Dict[str, Any]:
        """Executa workflow completo de geração de persona"""
        logger.info("Iniciando workflow de geração de persona")
        
        result = self.generate_persona(channel_id)
        
        if result['success']:
            logger.info("Workflow de geração de persona concluído com sucesso")
        else:
            logger.error(f"Erro no workflow de geração de persona: {result.get('error', 'Erro desconhecido')}")
        
        return result


def create_persona_generator_crew_agent() -> Agent:
    """Cria agente CrewAI para geração de persona"""
    return Agent(
        role='Gerador de Persona de Marketing',
        goal='Criar persona detalhada e acionável baseada em todas as análises de comentários',
        backstory="""Você é um especialista em marketing digital e criação de personas com 
        foco no mercado de parentalidade. Sua expertise está em sintetizar grandes volumes 
        de dados comportamentais e emocionais em personas precisas e acionáveis que podem 
        ser usadas para estratégias de marketing, desenvolvimento de produtos e comunicação.""",
        verbose=True,
        allow_delegation=False
    )


def create_persona_generation_task(agent: Agent, channel_id: int) -> Task:
    """Cria task CrewAI para geração de persona"""
    return Task(
        description=f"""
        Gere uma persona completa e detalhada baseada em todas as análises do canal ID: {channel_id}
        
        Suas responsabilidades:
        1. Consolidar todos os insights de dores, desejos, sonhos e frustrações
        2. Identificar padrões demográficos e comportamentais
        3. Criar perfil psicográfico detalhado
        4. Definir canais de comunicação preferenciais
        5. Identificar oportunidades de negócio específicas
        6. Criar narrativa humanizada da persona
        
        A persona deve incluir:
        - Nome e dados demográficos realistas
        - Top 5 dores mais significativas
        - Top 5 desejos mais comuns
        - Sonhos e aspirações para os filhos
        - Frustrações frequentes
        - Palavras e expressões que geram conexão
        - Oportunidades de produtos/serviços
        - Tom de comunicação ideal
        - Descrição narrativa envolvente
        
        Base-se exclusivamente nos dados analisados e seja específico e acionável.
        """,
        agent=agent,
        expected_output="Persona completa com relatório detalhado em JSON e Markdown"
    )
