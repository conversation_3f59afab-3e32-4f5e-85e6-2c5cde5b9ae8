"""
Gerenciador de Projetos do PersonaCreator
Permite criar e gerenciar múltiplos projetos isolados
"""
import json
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from loguru import logger

from database import DatabaseManager


class ProjectManager:
    """Gerencia projetos isolados do PersonaCreator"""
    
    def __init__(self):
        self.projects_dir = Path("projects")
        self.projects_dir.mkdir(exist_ok=True)
        self.current_project = None
    
    def create_project(self, name: str, description: str = "", 
                      context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Cria um novo projeto"""
        # Sanitiza nome do projeto
        project_id = self._sanitize_project_name(name)
        project_dir = self.projects_dir / project_id
        
        if project_dir.exists():
            raise ValueError(f"Projeto '{name}' já existe!")
        
        # Cria estrutura do projeto
        project_dir.mkdir(parents=True)
        (project_dir / "channels").mkdir()
        (project_dir / "personas").mkdir()
        (project_dir / "exports").mkdir()
        (project_dir / "output").mkdir()  # Pasta para JSONs e arquivos temporários
        (project_dir / "logs").mkdir()
        
        # Configurações do projeto
        project_config = {
            "id": project_id,
            "name": name,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "context": context or {},
            "channels_analyzed": [],
            "personas_created": 0,
            "status": "active"
        }
        
        # Salva configuração
        config_file = project_dir / "project.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(project_config, f, indent=2, ensure_ascii=False)

        # Define projeto atual com estrutura completa
        self.current_project = {
            'config': project_config,
            'dir': project_dir,
            'db_path': project_dir / "database.db"
        }

        logger.info(f"Projeto '{name}' criado em: {project_dir}")
        return project_config
    
    def list_projects(self) -> List[Dict[str, Any]]:
        """Lista todos os projetos"""
        projects = []
        
        for project_dir in self.projects_dir.iterdir():
            if project_dir.is_dir():
                config_file = project_dir / "project.json"
                if config_file.exists():
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            project_config = json.load(f)
                            
                        # Adiciona estatísticas
                        stats = self._get_project_stats(project_dir)
                        project_config.update(stats)
                        
                        projects.append(project_config)
                    except Exception as e:
                        logger.warning(f"Erro ao carregar projeto {project_dir}: {e}")
        
        return sorted(projects, key=lambda x: x['updated_at'], reverse=True)
    
    def load_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Carrega um projeto específico"""
        project_dir = self.projects_dir / project_id
        config_file = project_dir / "project.json"
        
        if not config_file.exists():
            return None
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                project_config = json.load(f)
            
            # Atualiza projeto atual
            self.current_project = {
                'config': project_config,
                'dir': project_dir,
                'db_path': project_dir / "database.db"
            }
            
            return project_config
            
        except Exception as e:
            logger.error(f"Erro ao carregar projeto {project_id}: {e}")
            return None
    
    def get_project_database(self, project_id: str = None) -> DatabaseManager:
        """Obtém banco de dados isolado do projeto"""
        if project_id:
            project_dir = self.projects_dir / project_id
        elif self.current_project:
            project_dir = self.current_project['dir']
        else:
            raise ValueError("Nenhum projeto especificado ou carregado")
        
        db_path = project_dir / "database.db"
        
        # Cria DatabaseManager com caminho específico
        db_manager = DatabaseManager(str(db_path))
        db_manager.init_database()
        
        return db_manager
    
    def update_project(self, project_id: str, updates: Dict[str, Any]):
        """Atualiza configurações do projeto"""
        project_dir = self.projects_dir / project_id
        config_file = project_dir / "project.json"
        
        if not config_file.exists():
            raise ValueError(f"Projeto {project_id} não encontrado")
        
        # Carrega configuração atual
        with open(config_file, 'r', encoding='utf-8') as f:
            project_config = json.load(f)
        
        # Aplica atualizações
        project_config.update(updates)
        project_config['updated_at'] = datetime.now().isoformat()
        
        # Salva configuração atualizada
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(project_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Projeto {project_id} atualizado")
    
    def delete_project(self, project_id: str, confirm: bool = False):
        """Deleta um projeto (com confirmação)"""
        if not confirm:
            raise ValueError("Confirmação necessária para deletar projeto")
        
        project_dir = self.projects_dir / project_id
        
        if not project_dir.exists():
            raise ValueError(f"Projeto {project_id} não encontrado")
        
        # Remove diretório completo
        shutil.rmtree(project_dir)
        logger.info(f"Projeto {project_id} deletado")
    
    def export_project(self, project_id: str, export_path: str = None) -> str:
        """Exporta projeto completo"""
        project_dir = self.projects_dir / project_id
        
        if not project_dir.exists():
            raise ValueError(f"Projeto {project_id} não encontrado")
        
        if not export_path:
            export_path = f"export_{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
        
        # Cria arquivo ZIP
        import zipfile
        with zipfile.ZipFile(export_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in project_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(project_dir)
                    zipf.write(file_path, arcname)
        
        logger.info(f"Projeto {project_id} exportado para: {export_path}")
        return export_path
    
    def get_project_channels(self, project_id: str = None) -> List[Dict[str, Any]]:
        """Lista canais analisados no projeto"""
        if project_id:
            project_dir = self.projects_dir / project_id
        elif self.current_project:
            project_dir = self.current_project['dir']
        else:
            return []
        
        channels_dir = project_dir / "channels"
        channels = []
        
        for channel_dir in channels_dir.iterdir():
            if channel_dir.is_dir():
                parts_file = channel_dir / "parts_info.json"
                if parts_file.exists():
                    try:
                        with open(parts_file, 'r', encoding='utf-8') as f:
                            parts_info = json.load(f)
                        
                        channels.append({
                            'name': channel_dir.name,
                            'url': parts_info.get('channel_url', ''),
                            'total_videos': parts_info.get('total_videos', 0),
                            'total_parts': parts_info.get('total_parts', 0),
                            'created_at': parts_info.get('created_at', '')
                        })
                    except Exception as e:
                        logger.warning(f"Erro ao carregar canal {channel_dir}: {e}")
        
        return channels
    
    def get_project_personas(self, project_id: str = None) -> List[Dict[str, Any]]:
        """Lista personas criadas no projeto"""
        if project_id:
            project_dir = self.projects_dir / project_id
        elif self.current_project:
            project_dir = self.current_project['dir']
        else:
            return []
        
        personas_dir = project_dir / "personas"
        personas = []
        
        # Busca personas em todos os canais
        channels_dir = project_dir / "channels"
        for channel_dir in channels_dir.iterdir():
            if channel_dir.is_dir():
                channel_personas_dir = channel_dir / "personas"
                if channel_personas_dir.exists():
                    for persona_file in channel_personas_dir.glob("*.json"):
                        try:
                            with open(persona_file, 'r', encoding='utf-8') as f:
                                persona_data = json.load(f)
                            
                            personas.append({
                                'file': str(persona_file),
                                'channel': channel_dir.name,
                                'name': persona_data.get('persona_data', {}).get('nome_persona', 'Sem nome'),
                                'confidence': persona_data.get('persona_data', {}).get('score_confianca', 0),
                                'created_at': persona_data.get('created_at', '')
                            })
                        except Exception as e:
                            logger.warning(f"Erro ao carregar persona {persona_file}: {e}")
        
        return sorted(personas, key=lambda x: x['created_at'], reverse=True)
    
    def _sanitize_project_name(self, name: str) -> str:
        """Sanitiza nome do projeto para usar como ID"""
        import re
        # Remove caracteres especiais e substitui espaços por underscore
        sanitized = re.sub(r'[^\w\s-]', '', name.lower())
        sanitized = re.sub(r'[-\s]+', '_', sanitized)
        return sanitized.strip('_')
    
    def _get_project_stats(self, project_dir: Path) -> Dict[str, Any]:
        """Obtém estatísticas do projeto"""
        stats = {
            'channels_count': 0,
            'personas_count': 0,
            'total_videos_analyzed': 0
        }
        
        try:
            channels_dir = project_dir / "channels"
            if channels_dir.exists():
                stats['channels_count'] = len([d for d in channels_dir.iterdir() if d.is_dir()])
                
                # Conta vídeos e personas
                for channel_dir in channels_dir.iterdir():
                    if channel_dir.is_dir():
                        # Conta vídeos
                        parts_file = channel_dir / "parts_info.json"
                        if parts_file.exists():
                            with open(parts_file, 'r', encoding='utf-8') as f:
                                parts_info = json.load(f)
                            stats['total_videos_analyzed'] += parts_info.get('total_videos', 0)
                        
                        # Conta personas
                        personas_dir = channel_dir / "personas"
                        if personas_dir.exists():
                            stats['personas_count'] += len(list(personas_dir.glob("*.json")))
        
        except Exception as e:
            logger.warning(f"Erro ao calcular estatísticas: {e}")
        
        return stats
