"""
Agente Conversacional Orquestrador
Responsável por conversar com o usuário e orquestrar todo o processo de análise
"""
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.prompt import Prompt, Confirm
from rich.table import Table
from rich.text import Text
from rich.markdown import Markdown

from database import DatabaseManager
from ai_utils import LLaMAClient
from project_manager import ProjectManager
from agents.content_curator import ContentCuratorAgent
from agents.priority_analyzer import PriorityAnalyzerAgent
from agents.comment_extractor import CommentExtractorAgent
from agents.comment_classifier import CommentClassifierAgent
from agents.sentiment_analyzer import SentimentAnalyzerAgent
from agents.persona_generator import PersonaGeneratorAgent
from agents.parts_manager import PartsManager
from config import TARGET_CONTEXT_KEYWORDS


class ConversationalOrchestrator:
    """Agente que conversa com o usuário e orquestra todo o processo"""

    def __init__(self, llama_client: LLaMAClient):
        self.llama = llama_client
        self.console = Console()
        self.project_manager = ProjectManager()

        # Estado da conversa
        self.user_context = {}
        self.analysis_config = {}
        self.current_step = "inicio"
        self.current_project = None
        self.db = None  # Será definido após selecionar projeto
        
        # Agentes especializados (serão inicializados após selecionar projeto)
        self.content_curator = None
        self.priority_analyzer = None
        self.comment_extractor = None
        self.comment_classifier = None
        self.sentiment_analyzer = None
        self.persona_generator = None
        self.parts_manager = None
    
    def start_conversation(self, channel_url: str = None):
        """Inicia a conversa com o usuário"""
        # Etapa 0: Selecionar/Criar projeto
        if not self.select_or_create_project():
            return

        # Etapa 1: Entender o contexto
        if self.collect_user_context(channel_url) == False:
            return
        
        # Etapa 2: Curadoria conversacional
        self.conversational_curation()
        
        # Etapa 3: Priorização interativa
        self.interactive_prioritization()
        
        # Etapa 4: Extração de comentários
        self.comment_extraction_chat()
        
        # Etapa 5: Classificação personalizada
        self.custom_classification()
        
        # Etapa 6: Análise de sentimentos guiada
        self.guided_sentiment_analysis()
        
        # Etapa 7: Geração de persona colaborativa
        self.collaborative_persona_generation()
    
    # show_welcome removido - já é chamado no persona_creator.py
    
    def collect_user_context(self, channel_url: str = None):
        """Coleta informações sobre o contexto do usuário"""
        try:
            self.console.print("\n" + "="*60)
            self.console.print("📋 ETAPA 1: Vamos entender seu contexto", style="bold blue")
            self.console.print("="*60)

            # Pergunta sobre o público-alvo/nicho
            nicho = Prompt.ask(
                "\n🎯 [bold]Qual é o público-alvo/nicho que você quer analisar?[/bold]\n"
                "   (ex: 'mães de crianças autistas', 'empreendedores digitais', 'fitness feminino')"
            ).strip()

            # Pergunta sobre o contexto de análise (sua pergunta original!)
            contexto_analise = Prompt.ask(
                "\n📋 [bold]O que especificamente você quer extrair dos vídeos?[/bold]\n"
                "   Descreva detalhadamente o que busca (dores, medos, desejos, linguagem, etc.)"
            ).strip()

            # Pergunta sobre o objetivo
            objetivo = Prompt.ask(
                "\n🎯 [bold]Qual seu objetivo com essa persona?[/bold]\n"
                "   (ex: 'criar conteúdo', 'vender produto', 'entender audiência')"
            )

            # Pergunta pela URL do canal se não foi fornecida
            if not channel_url:
                self.console.print(f"\n🔗 Agora preciso saber qual canal do YouTube analisar para '{nicho}'.")
                channel_url = Prompt.ask(
                    "\n📺 [bold]Digite a URL do canal do YouTube:[/bold]\n"
                    "   (ex: 'https://www.youtube.com/@mayragaiato', 'https://www.youtube.com/c/canalexemplo')"
                )

            # Pergunta sobre palavras-chave
            self.console.print(f"\n💡 Para analisar '{nicho}', vou usar duas estratégias:")
            self.console.print("   🔍 [cyan]Busca por palavras-chave[/cyan] (rápida)")
            self.console.print("   🤖 [cyan]Análise contextual com IA[/cyan] (inteligente)")

            self.console.print(f"\n📝 As palavras-chave servem como [bold]filtro inicial[/bold], mas a IA também vai analisar o [bold]contexto e significado[/bold] dos títulos.")
            self.console.print("   Exemplo: 'Como lidar com crises' será identificado como relevante para 'autismo' mesmo sem a palavra literal.")

            keywords_input = Prompt.ask(
                "\n🔍 [bold]Quais palavras-chave principais devo buscar?[/bold]\n"
                "   Digite separadas por vírgula (ex: 'autismo, TEA, espectro autista, comportamento')"
            )

            keywords = [k.strip() for k in keywords_input.split(',')]

            # Salva o contexto
            self.user_context = {
                'channel_url': channel_url,
                'nicho': nicho,
                'contexto_analise': contexto_analise,
                'objetivo': objetivo,
                'keywords': keywords,
                'timestamp': datetime.now().isoformat()
            }

            # Confirma com o usuário
            self.show_context_summary()

        except (EOFError, KeyboardInterrupt):
            self.console.print("\n\n👋 Operação cancelada pelo usuário.")
            return False
        except Exception as e:
            self.console.print(f"\n❌ Erro ao coletar contexto: {e}")
            logger.error(f"Erro em collect_user_context: {e}")
            return False
    
    def show_context_summary(self):
        """Mostra resumo do contexto coletado"""
        try:
            table = Table(title="📋 Resumo do Seu Contexto")
            table.add_column("Campo", style="cyan")
            table.add_column("Valor", style="white")

            table.add_row("🎯 Público-alvo", self.user_context['nicho'])
            table.add_row("📋 Contexto de análise", self.user_context['contexto_analise'])

            table.add_row("🎯 Objetivo", self.user_context['objetivo'])
            table.add_row("🔍 Palavras-chave", ", ".join(self.user_context['keywords']))
            table.add_row("🔗 Canal", self.user_context['channel_url'] or "Não informado")

            self.console.print("\n")
            self.console.print(table)

            # Opção para ver contexto completo se foi truncado
            if len(self.user_context['contexto_analise']) > 150:
                if Confirm.ask("\n👁️ Quer ver o contexto de análise completo?"):
                    self.console.print(f"\n📋 [bold]Contexto completo:[/bold]")
                    self.console.print(f"[dim]{self.user_context['contexto_analise']}[/dim]")

            if not Confirm.ask("\n✅ Essas informações estão corretas?"):
                return self.collect_user_context(self.user_context.get('channel_url'))

        except (EOFError, KeyboardInterrupt):
            self.console.print("\n\n👋 Operação cancelada pelo usuário.")
            return False
        except Exception as e:
            self.console.print(f"\n❌ Erro ao mostrar resumo: {e}")
            logger.error(f"Erro em show_context_summary: {e}")
            return False
    
    def conversational_curation(self):
        """Curadoria de conteúdo com análise por partes"""
        self.console.print("\n" + "="*60)
        self.console.print("🔍 ETAPA 2: Vamos encontrar vídeos relevantes", style="bold green")
        self.console.print("="*60)

        # Explica o que vai acontecer
        self.console.print(f"\n📋 [bold]O que vou fazer agora:[/bold]")
        self.console.print(f"   1️⃣ Extrair TODOS os vídeos do canal: {self.user_context['channel_url']}")
        self.console.print(f"   2️⃣ Salvar lista completa em arquivo JSON")
        self.console.print(f"   3️⃣ Salvar no banco de dados local")
        self.console.print(f"   4️⃣ Mostrar estatísticas dos vídeos encontrados")

        self.console.print(f"\n⚠️ [yellow]IMPORTANTE:[/yellow]")
        self.console.print(f"   • Esta etapa [bold]NÃO[/bold] usa IA ainda")
        self.console.print(f"   • Apenas extrai e organiza os vídeos")
        self.console.print(f"   • A análise com IA será na próxima etapa")

        if not Confirm.ask(f"\n▶️ Posso começar a extração do canal?"):
            return

        # Executa APENAS extração de vídeos (sem IA)
        try:
            self.console.print(f"\n🚀 [bold green]Iniciando extração...[/bold green]")
            self.console.print(f"🔗 Canal: {self.user_context['channel_url']}")

            with self.console.status("[bold green]Extraindo vídeos do canal...") as status:
                result = self.content_curator.extract_and_save_videos_only(
                    self.user_context['channel_url']
                )

            self.console.print(f"\n🔍 [cyan]Resultado da extração:[/cyan] {result}")

            if not result['success']:
                error_msg = result.get('error', 'Erro desconhecido')
                self.console.print(f"❌ [red]Erro na extração:[/red] {error_msg}")

                if Confirm.ask("🔄 Quer tentar novamente?"):
                    return self.conversational_curation()
                else:
                    self.console.print("❌ Extração cancelada pelo usuário.")
                    return False

            # Salva informações para próximas etapas
            self.user_context['channel_id'] = result['channel_id']
            self.user_context['total_videos'] = result['total_videos']
            self.user_context['json_file'] = result['json_file']

            # Mostra resultados da extração
            self.show_extraction_results(result)

            # Pergunta se quer continuar para análise com IA
            if Confirm.ask(f"\n🤖 Quer continuar para a ETAPA 3 (análise com IA)?"):
                return self.conversational_ai_analysis()
            else:
                self.console.print(f"\n✅ Vídeos salvos em: {result['json_file']}")
                self.console.print(f"📊 Total de vídeos extraídos: {result['total_videos']}")
                return True

        except Exception as e:
            self.console.print(f"❌ [red]Erro inesperado:[/red] {str(e)}")
            import traceback
            self.console.print(f"🔍 [dim]Detalhes:[/dim] {traceback.format_exc()}")

            if Confirm.ask("🔄 Quer tentar novamente?"):
                return self.conversational_curation()
            else:
                self.console.print("❌ Extração cancelada devido ao erro.")
                return False

    def show_extraction_results(self, result: Dict[str, Any]):
        """Mostra resultados da extração de vídeos (ETAPA 2)"""
        self.console.print(f"\n" + "="*60)
        self.console.print("✅ EXTRAÇÃO CONCLUÍDA!", style="bold green")
        self.console.print("="*60)

        # Estatísticas
        table = Table(title="📊 Resultados da Extração")
        table.add_column("Métrica", style="cyan")
        table.add_column("Valor", style="green")

        table.add_row("🎥 Total de vídeos extraídos", str(result['total_videos']))
        table.add_row("💾 Arquivo JSON salvo", result['json_file'])
        table.add_row("🆔 ID do canal no banco", str(result['channel_id']))

        self.console.print(table)

        # Mostra amostra dos vídeos
        if result.get('videos_sample'):
            self.console.print(f"\n📋 [bold]Amostra dos vídeos encontrados:[/bold]")
            for i, video in enumerate(result['videos_sample'], 1):
                self.console.print(f"   {i}. {video['title']}")
                self.console.print(f"      👀 {video.get('view_count', 0):,} visualizações")

        self.console.print(f"\n💡 [yellow]Próximo passo:[/yellow] Análise com IA para identificar vídeos relevantes")

    def conversational_ai_analysis(self):
        """ETAPA 3: Análise com IA dos vídeos extraídos"""
        self.console.print("\n" + "="*60)
        self.console.print("🤖 ETAPA 3: Análise com IA", style="bold green")
        self.console.print("="*60)

        self.console.print("🚧 [yellow]Esta funcionalidade será implementada em breve![/yellow]")
        self.console.print("📋 Aqui será feita a análise por lotes dos vídeos extraídos.")
        return

    def show_curation_results(self, result: Dict[str, Any]):
        """Mostra resultados da curadoria"""
        total_videos = result.get('total_videos', 0)
        relevant_videos = result.get('relevant_videos', 0)
        
        self.console.print(f"\n✅ Encontrei {total_videos} vídeos, {relevant_videos} são relevantes!")
        
        if relevant_videos == 0:
            self.console.print("🤔 Nenhum vídeo relevante encontrado.")
            if Confirm.ask("Quer ajustar as palavras-chave?"):
                return self.collect_user_context(self.user_context['channel_url'])
            return
        
        # Mostra alguns exemplos
        videos = self.get_relevant_videos_sample()
        if videos:
            self.show_video_samples(videos)
        
        # Pergunta se quer continuar
        if not Confirm.ask(f"\n🚀 Continuar com esses {relevant_videos} vídeos?"):
            if Confirm.ask("Quer ajustar as palavras-chave?"):
                return self.collect_user_context(self.user_context['channel_url'])
    
    def get_relevant_videos_sample(self) -> List[Dict[str, Any]]:
        """Busca amostra de vídeos relevantes"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT title, view_count, comment_count, context_relevance_score
                    FROM videos 
                    WHERE is_relevant = 1 
                    ORDER BY context_relevance_score DESC 
                    LIMIT 5
                """)
                return [dict(row) for row in cursor.fetchall()]
        except:
            return []
    
    def show_video_samples(self, videos: List[Dict[str, Any]]):
        """Mostra amostra de vídeos encontrados"""
        table = Table(title="🎬 Exemplos de Vídeos Relevantes Encontrados")
        table.add_column("Título", style="cyan", width=40)
        table.add_column("Views", style="yellow")
        table.add_column("Comentários", style="green")
        table.add_column("Relevância", style="blue")
        
        for video in videos:
            relevance = f"{video['context_relevance_score']:.1%}" if video['context_relevance_score'] else "N/A"
            table.add_row(
                video['title'][:37] + "..." if len(video['title']) > 40 else video['title'],
                f"{video['view_count']:,}" if video['view_count'] else "N/A",
                f"{video['comment_count']:,}" if video['comment_count'] else "N/A",
                relevance
            )
        
        self.console.print("\n")
        self.console.print(table)
    
    def interactive_prioritization(self):
        """Priorização interativa de vídeos"""
        self.console.print("\n" + "="*60)
        self.console.print("📊 ETAPA 3: Vamos priorizar os vídeos para análise", style="bold yellow")
        self.console.print("="*60)
        
        # Pergunta quantos vídeos analisar
        max_videos = Prompt.ask(
            "\n📈 [bold]Quantos vídeos você quer analisar?[/bold]\n"
            "   (Recomendo 3-5 para começar)",
            default="3"
        )
        
        try:
            max_videos = int(max_videos)
        except:
            max_videos = 3
        
        self.console.print(f"\n🔄 Priorizando os {max_videos} melhores vídeos...")
        
        # Executa priorização
        try:
            channel_id = self.get_current_channel_id()
            result = self.priority_analyzer.run_prioritization_workflow(channel_id)
            
            if result['success']:
                self.show_prioritization_results(result, max_videos)
            else:
                self.console.print("❌ Erro na priorização.")
                
        except Exception as e:
            self.console.print(f"❌ Erro: {e}")
    
    def get_current_channel_id(self) -> int:
        """Busca o ID do canal atual"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute("SELECT id FROM channels ORDER BY id DESC LIMIT 1")
                row = cursor.fetchone()
                return row[0] if row else 1
        except:
            return 1

    def show_prioritization_results(self, result: Dict[str, Any], max_videos: int):
        """Mostra resultados da priorização"""
        prioritized_videos = self.get_prioritized_videos(max_videos)

        if not prioritized_videos:
            self.console.print("❌ Nenhum vídeo priorizado encontrado.")
            return

        table = Table(title=f"🏆 Top {len(prioritized_videos)} Vídeos Priorizados")
        table.add_column("#", style="bold")
        table.add_column("Título", style="cyan", width=35)
        table.add_column("Score", style="green")
        table.add_column("Comentários", style="yellow")

        for i, video in enumerate(prioritized_videos, 1):
            table.add_row(
                str(i),
                video['title'][:32] + "..." if len(video['title']) > 35 else video['title'],
                f"{video.get('priority_score', 0):.2f}",
                str(video.get('comment_count', 0))
            )

        self.console.print("\n")
        self.console.print(table)

        if not Confirm.ask(f"\n🚀 Continuar com esses {len(prioritized_videos)} vídeos?"):
            return self.interactive_prioritization()

    def get_prioritized_videos(self, limit: int) -> List[Dict[str, Any]]:
        """Busca vídeos priorizados"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT title, priority_score, comment_count, view_count
                    FROM videos
                    WHERE is_relevant = 1 AND priority_score > 0
                    ORDER BY priority_score DESC
                    LIMIT ?
                """, (limit,))
                return [dict(row) for row in cursor.fetchall()]
        except:
            return []

    def comment_extraction_chat(self):
        """Extração de comentários com conversa"""
        self.console.print("\n" + "="*60)
        self.console.print("💬 ETAPA 4: Vamos extrair os comentários", style="bold magenta")
        self.console.print("="*60)

        self.console.print("\n🔄 Extraindo comentários dos vídeos priorizados...")

        try:
            channel_id = self.get_current_channel_id()
            result = self.comment_extractor.run_extraction_workflow(channel_id)

            if result['success']:
                self.show_extraction_results(result)
            else:
                self.console.print("❌ Erro na extração de comentários.")

        except Exception as e:
            self.console.print(f"❌ Erro: {e}")

    def show_extraction_results(self, result: Dict[str, Any]):
        """Mostra resultados da extração"""
        total_comments = result.get('total_comments', 0)

        self.console.print(f"\n✅ Extraí {total_comments} comentários relevantes!")

        if total_comments == 0:
            self.console.print("🤔 Nenhum comentário encontrado.")
            return

        # Mostra alguns exemplos
        sample_comments = self.get_sample_comments()
        if sample_comments:
            self.show_comment_samples(sample_comments)

        if not Confirm.ask(f"\n🚀 Continuar com {total_comments} comentários?"):
            # Opção de ajustar filtros
            if Confirm.ask("Quer ajustar os filtros de comentários?"):
                self.adjust_comment_filters()

    def get_sample_comments(self) -> List[Dict[str, Any]]:
        """Busca amostra de comentários"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT c.text, c.like_count, v.title as video_title
                    FROM comments c
                    JOIN videos v ON c.video_id = v.id
                    ORDER BY c.like_count DESC, LENGTH(c.text) DESC
                    LIMIT 3
                """)
                return [dict(row) for row in cursor.fetchall()]
        except:
            return []

    def show_comment_samples(self, comments: List[Dict[str, Any]]):
        """Mostra amostra de comentários"""
        self.console.print("\n📝 Exemplos de comentários extraídos:")

        for i, comment in enumerate(comments, 1):
            text = comment['text'][:100] + "..." if len(comment['text']) > 100 else comment['text']

            panel = Panel(
                f"💬 {text}\n\n👍 {comment['like_count']} likes | 🎬 {comment['video_title'][:30]}...",
                title=f"Comentário {i}",
                border_style="dim"
            )
            self.console.print(panel)

    def custom_classification(self):
        """Classificação personalizada com tags customizadas"""
        self.console.print("\n" + "="*60)
        self.console.print("🏷️ ETAPA 5: Vamos classificar os comentários", style="bold red")
        self.console.print("="*60)

        # Pergunta sobre tags personalizadas
        self.console.print(f"\n💡 Agora vou classificar os comentários sobre '{self.user_context['nicho']}'.")

        use_custom_tags = Confirm.ask(
            "\n🎯 [bold]Quer definir tags personalizadas?[/bold]\n"
            "   (Se não, usarei tags padrão como 'família', 'educação', 'saúde')"
        )

        if use_custom_tags:
            self.collect_custom_tags()

        # Executa classificação
        self.console.print("\n🔄 Classificando comentários...")

        try:
            channel_id = self.get_current_channel_id()
            result = self.comment_classifier.run_classification_workflow(channel_id)

            if result['success']:
                self.show_classification_results(result)
            else:
                self.console.print("❌ Erro na classificação.")

        except Exception as e:
            self.console.print(f"❌ Erro: {e}")

    def collect_custom_tags(self):
        """Coleta tags personalizadas do usuário"""
        self.console.print("\n🏷️ Vamos criar tags personalizadas para seu nicho!")

        tags_input = Prompt.ask(
            f"\n🎯 [bold]Que tags você quer identificar nos comentários sobre '{self.user_context['nicho']}'?[/bold]\n"
            "   Digite separadas por vírgula (ex: 'terapia, comportamento, escola, diagnóstico')"
        )

        custom_tags = [tag.strip() for tag in tags_input.split(',')]

        # Cria dicionário de tags com palavras-chave
        tag_dict = {}
        for tag in custom_tags:
            keywords_input = Prompt.ask(
                f"\n🔍 [bold]Quais palavras-chave identificam '{tag}'?[/bold]\n"
                f"   (ex: para 'terapia': 'terapeuta, ABA, fonoaudiologia')",
                default=tag  # usa a própria tag como padrão
            )
            tag_dict[tag] = [k.strip() for k in keywords_input.split(',')]

        # Salva tags personalizadas
        self.analysis_config['custom_tags'] = tag_dict

        # Atualiza o classificador temporariamente
        self.comment_classifier.comment_tags.update(tag_dict)

    def show_classification_results(self, result: Dict[str, Any]):
        """Mostra resultados da classificação"""
        stats = result.get('classification_stats', {})
        total_classified = stats.get('comments_with_tags', 0)
        total_processed = stats.get('total_processed', 0)

        self.console.print(f"\n✅ Classifiquei {total_classified}/{total_processed} comentários!")

        # Mostra distribuição de tags
        tag_distribution = stats.get('tag_distribution', {})
        if tag_distribution:
            self.show_tag_distribution(tag_distribution)

        if not Confirm.ask(f"\n🚀 Continuar com essa classificação?"):
            if Confirm.ask("Quer ajustar as tags?"):
                return self.custom_classification()

    def show_tag_distribution(self, tag_distribution: Dict[str, int]):
        """Mostra distribuição das tags"""
        table = Table(title="🏷️ Distribuição de Tags")
        table.add_column("Tag", style="cyan")
        table.add_column("Comentários", style="green")
        table.add_column("Porcentagem", style="yellow")

        total = sum(tag_distribution.values())

        for tag, count in sorted(tag_distribution.items(), key=lambda x: x[1], reverse=True):
            percentage = f"{(count/total)*100:.1f}%" if total > 0 else "0%"
            table.add_row(tag, str(count), percentage)

        self.console.print("\n")
        self.console.print(table)

    def guided_sentiment_analysis(self):
        """Análise de sentimentos guiada"""
        self.console.print("\n" + "="*60)
        self.console.print("❤️ ETAPA 6: Vamos analisar sentimentos e extrair insights", style="bold cyan")
        self.console.print("="*60)

        # Pergunta quais tags analisar
        available_tags = self.get_available_tags()
        if not available_tags:
            self.console.print("❌ Nenhuma tag disponível para análise.")
            return

        self.console.print(f"\n💡 Encontrei essas tags: {', '.join(available_tags)}")

        selected_tags_input = Prompt.ask(
            "\n🎯 [bold]Quais tags você quer analisar em profundidade?[/bold]\n"
            "   Digite separadas por vírgula (ou 'todas' para analisar todas)",
            default="todas"
        )

        if selected_tags_input.lower() == "todas":
            selected_tags = available_tags
        else:
            selected_tags = [tag.strip() for tag in selected_tags_input.split(',')]
            selected_tags = [tag for tag in selected_tags if tag in available_tags]

        if not selected_tags:
            self.console.print("❌ Nenhuma tag válida selecionada.")
            return self.guided_sentiment_analysis()

        self.console.print(f"\n🔄 Analisando sentimentos para: {', '.join(selected_tags)}")

        try:
            channel_id = self.get_current_channel_id()
            result = self.sentiment_analyzer.run_analysis_workflow(channel_id, selected_tags)

            if result['success']:
                self.show_sentiment_results(result)
            else:
                self.console.print("❌ Erro na análise de sentimentos.")

        except Exception as e:
            self.console.print(f"❌ Erro: {e}")

    def get_available_tags(self) -> List[str]:
        """Busca tags disponíveis"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT DISTINCT tag_name
                    FROM comment_tags
                    ORDER BY tag_name
                """)
                return [row[0] for row in cursor.fetchall()]
        except:
            return []

    def show_sentiment_results(self, result: Dict[str, Any]):
        """Mostra resultados da análise de sentimentos"""
        analysis_stats = result.get('analysis_stats', {})
        insights_found = analysis_stats.get('comments_with_insights', 0)
        total_analyzed = analysis_stats.get('total_analyzed', 0)

        self.console.print(f"\n✅ Analisei {total_analyzed} comentários e encontrei {insights_found} insights!")

        # Mostra preview dos insights
        insights_preview = self.get_insights_preview()
        if insights_preview:
            self.show_insights_preview(insights_preview)

        if not Confirm.ask(f"\n🚀 Continuar com esses insights?"):
            if Confirm.ask("Quer analisar outras tags?"):
                return self.guided_sentiment_analysis()

    def get_insights_preview(self) -> Dict[str, List[str]]:
        """Busca preview dos insights"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT analysis_type, extracted_content
                    FROM comment_analysis
                    WHERE confidence_score > 0.5
                    ORDER BY confidence_score DESC
                    LIMIT 10
                """)

                insights = {'dores': [], 'desejos': [], 'palavras_magicas': []}

                for row in cursor.fetchall():
                    analysis_type = row[0]
                    content = json.loads(row[1]) if row[1] else []

                    if analysis_type in insights and content:
                        insights[analysis_type].extend(content[:2])  # Máximo 2 por tipo

                return insights
        except:
            return {}

    def show_insights_preview(self, insights: Dict[str, List[str]]):
        """Mostra preview dos insights"""
        self.console.print("\n🔍 Preview dos insights encontrados:")

        for insight_type, items in insights.items():
            if items:
                title = {
                    'dores': '😰 Principais Dores',
                    'desejos': '✨ Principais Desejos',
                    'palavras_magicas': '🎯 Palavras Mágicas'
                }.get(insight_type, insight_type.title())

                panel_content = "\n".join([f"• {item}" for item in items[:3]])

                panel = Panel(
                    panel_content,
                    title=title,
                    border_style="dim"
                )
                self.console.print(panel)

    def collaborative_persona_generation(self):
        """Geração colaborativa da persona"""
        self.console.print("\n" + "="*60)
        self.console.print("👤 ETAPA 7: Vamos criar sua persona!", style="bold green")
        self.console.print("="*60)

        # Pergunta sobre personalização da persona
        persona_name = Prompt.ask(
            "\n🎭 [bold]Como você quer chamar sua persona?[/bold]\n"
            f"   (ex: 'Maria Mãe Autista', 'João Empreendedor')",
            default=f"Persona {self.user_context['nicho'].title()}"
        )

        # Pergunta sobre foco da persona
        focus_areas = Prompt.ask(
            "\n🎯 [bold]Em que aspectos você quer focar a persona?[/bold]\n"
            "   (ex: 'dores e medos', 'desejos e sonhos', 'comportamento de compra')",
            default="dores, desejos, comportamento"
        )

        self.console.print(f"\n🔄 Gerando persona '{persona_name}'...")

        try:
            channel_id = self.get_current_channel_id()
            result = self.persona_generator.run_persona_generation_workflow(channel_id)

            if result['success']:
                # Personaliza a persona com as informações do usuário
                self.customize_persona(result, persona_name, focus_areas)
                self.show_final_persona(result)
            else:
                self.console.print("❌ Erro na geração da persona.")

        except Exception as e:
            self.console.print(f"❌ Erro: {e}")

    def customize_persona(self, result: Dict[str, Any], name: str, focus: str):
        """Personaliza a persona com informações do usuário"""
        # Adiciona contexto do usuário à persona
        result['user_context'] = self.user_context
        result['persona_name'] = name
        result['focus_areas'] = focus
        result['custom_tags'] = self.analysis_config.get('custom_tags', {})

    def show_final_persona(self, result: Dict[str, Any]):
        """Mostra a persona final"""
        self.console.print("\n" + "="*60)
        self.console.print("🎉 SUA PERSONA ESTÁ PRONTA!", style="bold green")
        self.console.print("="*60)

        persona_data = result.get('persona_data', {})
        confidence = result.get('confidence_level', 0)

        # Mostra resumo da persona
        summary_text = f"""
# 👤 {result.get('persona_name', 'Sua Persona')}

**🎯 Nicho:** {self.user_context['nicho']}
**📊 Confiança:** {confidence:.0%}
**📈 Baseado em:** {result.get('total_comments_analyzed', 0)} comentários

## 🔍 Principais Características:
• **Demografia:** {persona_data.get('demografia', {}).get('idade', 'N/A')}
• **Principais Dores:** {len(persona_data.get('dores_principais', []))} identificadas
• **Principais Desejos:** {len(persona_data.get('desejos_principais', []))} identificados
• **Palavras Mágicas:** {len(persona_data.get('palavras_magicas', []))} encontradas
        """

        self.console.print(Panel(
            Markdown(summary_text),
            title="🎯 Sua Persona Personalizada",
            border_style="green"
        ))

        # Pergunta se quer ver detalhes
        if Confirm.ask("\n📋 Quer ver os detalhes completos da persona?"):
            self.show_detailed_persona(persona_data)

        # Pergunta sobre exportação
        if Confirm.ask("\n💾 Quer salvar a persona em um arquivo?"):
            self.export_persona(result)

        self.show_next_steps()

    def show_detailed_persona(self, persona_data: Dict[str, Any]):
        """Mostra detalhes completos da persona"""
        # Implementar visualização detalhada
        self.console.print("\n📋 Detalhes completos da persona:")
        self.console.print(json.dumps(persona_data, indent=2, ensure_ascii=False))

    def export_persona(self, result: Dict[str, Any]):
        """Exporta a persona para arquivo"""
        filename = f"persona_{self.user_context['nicho'].replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        try:
            with open(f"output/{filename}", 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)

            self.console.print(f"\n✅ Persona salva em: output/{filename}")
        except Exception as e:
            self.console.print(f"❌ Erro ao salvar: {e}")

    def show_next_steps(self):
        """Mostra próximos passos"""
        next_steps = """
# 🚀 Próximos Passos

Agora que você tem sua persona, pode:

1. **📝 Criar Conteúdo** baseado nas dores e desejos identificados
2. **🎯 Segmentar Anúncios** usando as características demográficas
3. **💬 Ajustar Linguagem** usando as palavras mágicas encontradas
4. **🛍️ Desenvolver Produtos** baseado nas oportunidades identificadas
5. **📊 Analisar Outros Canais** para expandir a persona

**Quer analisar outro canal ou refinar esta persona?**
        """

        self.console.print(Panel(
            Markdown(next_steps),
            title="🎯 O que fazer agora?",
            border_style="blue"
        ))

        if Confirm.ask("\n🔄 Quer analisar outro canal?"):
            new_channel = Prompt.ask("🔗 Digite a URL do novo canal:")
            return self.start_conversation(new_channel)

        self.console.print("\n🎉 Obrigado por usar o PersonaCreator! Até a próxima! 👋")

    def select_or_create_project(self) -> bool:
        """Permite selecionar projeto existente ou criar novo"""
        try:
            self.console.print("\n" + "="*60)
            self.console.print("📁 GERENCIAMENTO DE PROJETOS", style="bold blue")
            self.console.print("="*60)

            # Lista projetos existentes
            projects = self.project_manager.list_projects()

            if projects:
                self.show_existing_projects(projects)

                choice = Prompt.ask(
                    "\n🎯 [bold]O que você quer fazer?[/bold]\n"
                    "   1️⃣ Criar novo projeto\n"
                    "   2️⃣ Continuar projeto existente\n"
                    "   3️⃣ Ver detalhes de um projeto\n"
                    "   Digite sua escolha (1, 2 ou 3)"
                )

                if choice == "1":
                    return self.create_new_project()
                elif choice == "2":
                    return self.select_existing_project(projects)
                elif choice == "3":
                    return self.show_project_details(projects)
                else:
                    self.console.print("❌ Escolha inválida.")
                    return self.select_or_create_project()
            else:
                self.console.print("📝 Nenhum projeto encontrado. Vamos criar seu primeiro projeto!")
                return self.create_new_project()

        except (EOFError, KeyboardInterrupt):
            self.console.print("\n\n👋 Operação cancelada pelo usuário.")
            return False
        except Exception as e:
            self.console.print(f"\n❌ Erro inesperado: {e}")
            logger.error(f"Erro em select_or_create_project: {e}")
            return False

    def show_existing_projects(self, projects: List[Dict[str, Any]]):
        """Mostra projetos existentes"""
        table = Table(title="📁 Seus Projetos")
        table.add_column("ID", style="cyan")
        table.add_column("Nome", style="bold")
        table.add_column("Canais", style="green")
        table.add_column("Personas", style="yellow")
        table.add_column("Atualizado", style="dim")

        for project in projects:
            updated = project['updated_at'][:10]  # Apenas data
            table.add_row(
                project['id'],
                project['name'],
                str(project.get('channels_count', 0)),
                str(project.get('personas_count', 0)),
                updated
            )

        self.console.print("\n")
        self.console.print(table)

    def create_new_project(self) -> bool:
        """Cria um novo projeto"""
        try:
            self.console.print("\n🆕 Criando novo projeto...")

            name = Prompt.ask(
                "\n📝 [bold]Como você quer chamar seu projeto?[/bold]\n"
                "   (ex: 'Análise Canais Autismo', 'Projeto Fitness 2024')"
            )

            description = Prompt.ask(
                "\n📋 [bold]Descreva o objetivo deste projeto:[/bold]\n"
                "   (ex: 'Criar personas para pais de crianças autistas')",
                default=""
            )

            project_config = self.project_manager.create_project(name, description)

            # Carrega o projeto criado
            self.current_project = project_config
            self.db = self.project_manager.get_project_database(project_config['id'])

            # Inicializa agentes com o banco do projeto
            self._initialize_agents()

            self.console.print(f"\n✅ Projeto '{name}' criado com sucesso!")
            self.console.print(f"📁 ID do projeto: {project_config['id']}")

            return True

        except (EOFError, KeyboardInterrupt):
            self.console.print("\n\n👋 Operação cancelada pelo usuário.")
            return False
        except Exception as e:
            self.console.print(f"❌ Erro ao criar projeto: {e}")
            logger.error(f"Erro em create_new_project: {e}")
            return False

    def select_existing_project(self, projects: List[Dict[str, Any]]) -> bool:
        """Seleciona projeto existente"""
        self.console.print("\n📂 Selecionando projeto existente...")

        # Mostra opções numeradas
        self.console.print("\n📋 Projetos disponíveis:")
        for i, project in enumerate(projects, 1):
            self.console.print(f"   {i}️⃣ {project['name']} ({project['id']})")

        while True:
            choice = Prompt.ask(f"\n🎯 Digite o número do projeto (1-{len(projects)}) ou 'voltar'")

            if choice.lower() == 'voltar':
                return self.select_or_create_project()

            try:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(projects):
                    selected_project = projects[choice_idx]

                    # Carrega o projeto
                    project_config = self.project_manager.load_project(selected_project['id'])
                    if project_config:
                        self.current_project = project_config
                        self.db = self.project_manager.get_project_database(selected_project['id'])

                        # Inicializa agentes com o banco do projeto
                        self._initialize_agents()

                        self.console.print(f"\n✅ Projeto '{project_config['name']}' carregado!")
                        self.show_project_summary(selected_project)

                        return True
                    else:
                        self.console.print("❌ Erro ao carregar projeto.")
                        return False
                else:
                    self.console.print(f"❌ Número inválido. Digite de 1 a {len(projects)}.")
            except ValueError:
                self.console.print("❌ Digite um número válido ou 'voltar'.")

    def show_project_summary(self, project: Dict[str, Any]):
        """Mostra resumo do projeto carregado"""
        summary_text = f"""
## 📊 Resumo do Projeto

**Nome:** {project['name']}
**Descrição:** {project.get('description', 'Sem descrição')}
**Canais analisados:** {project.get('channels_count', 0)}
**Personas criadas:** {project.get('personas_count', 0)}
**Vídeos analisados:** {project.get('total_videos_analyzed', 0)}
**Última atualização:** {project['updated_at'][:10]}
        """

        self.console.print(Panel(
            Markdown(summary_text),
            title="📁 Projeto Carregado",
            border_style="green"
        ))

    def _initialize_agents(self):
        """Inicializa agentes com o banco do projeto atual"""
        if not self.db:
            raise ValueError("Banco de dados do projeto não definido")

        if not self.current_project:
            raise ValueError("Projeto atual não definido")

        # Agentes especializados com banco isolado E diretório do projeto
        project_dir = Path("projects") / self.current_project['id']

        # Configura log específico do projeto
        self._setup_project_logging(project_dir)

        self.content_curator = ContentCuratorAgent(self.db, self.llama, project_dir)
        self.priority_analyzer = PriorityAnalyzerAgent(self.db, self.llama)
        self.comment_extractor = CommentExtractorAgent(self.db)
        self.comment_classifier = CommentClassifierAgent(self.db, self.llama)
        self.sentiment_analyzer = SentimentAnalyzerAgent(self.db, self.llama)
        self.persona_generator = PersonaGeneratorAgent(self.db, self.llama)
        self.parts_manager = PartsManager(self.db, project_dir)

    def _setup_project_logging(self, project_dir: Path):
        """Configura logging específico do projeto"""
        project_log_file = project_dir / "logs" / "project.log"

        # Adiciona handler específico do projeto (sem remover o global)
        logger.add(
            project_log_file,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            level="DEBUG",
            rotation="1 day",
            retention="30 days",
            filter=lambda record: "conversational_orchestrator" in record["name"] or "content_curator" in record["name"]
        )

        logger.info(f"Log do projeto configurado: {project_log_file}")

    def show_project_details(self, projects: List[Dict[str, Any]]) -> bool:
        """Mostra detalhes de um projeto específico"""
        self.console.print("\n📋 Detalhes do projeto...")

        # Mostra opções numeradas
        self.console.print("\n📂 Projetos disponíveis:")
        for i, project in enumerate(projects, 1):
            self.console.print(f"   {i}️⃣ {project['name']} ({project['id']})")

        while True:
            choice = Prompt.ask(f"\n🎯 Digite o número do projeto (1-{len(projects)}) ou 'voltar'")

            if choice.lower() == 'voltar':
                return self.select_or_create_project()

            try:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(projects):
                    selected_project = projects[choice_idx]

                    # Mostra detalhes completos
                    self.show_project_summary(selected_project)

                    # Pergunta se quer continuar com este projeto
                    if Confirm.ask("\n🚀 Quer continuar com este projeto?"):
                        # Carrega o projeto
                        project_config = self.project_manager.load_project(selected_project['id'])
                        if project_config:
                            self.current_project = project_config
                            self.db = self.project_manager.get_project_database(selected_project['id'])
                            self._initialize_agents()
                            return True
                    else:
                        return self.select_or_create_project()
                else:
                    self.console.print(f"❌ Número inválido. Digite de 1 a {len(projects)}.")
            except ValueError:
                self.console.print("❌ Digite um número válido ou 'voltar'.")

    def get_relevant_videos_from_db(self) -> List[Dict[str, Any]]:
        """Busca vídeos relevantes do banco de dados"""
        try:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT video_id, title, description, upload_date, view_count,
                           comment_count, context_relevance_score
                    FROM videos
                    WHERE is_relevant = 1
                    ORDER BY upload_date DESC
                """)
                return [dict(row) for row in cursor.fetchall()]
        except:
            return []

    def show_channel_analysis(self, size_analysis: Dict[str, Any]):
        """Mostra análise do tamanho do canal"""
        total_videos = size_analysis['total_videos']
        suggestion = size_analysis['suggestion']

        self.console.print(f"\n📊 Análise do canal: {total_videos} vídeos relevantes encontrados")
        self.console.print(f"💡 {suggestion['message']}")

        if suggestion['parts']:
            table = Table(title="🔢 Opções de Divisão em Partes")
            table.add_column("Vídeos por Parte", style="cyan")
            table.add_column("Total de Partes", style="green")
            table.add_column("Tempo Estimado", style="yellow")

            for part_option in suggestion['parts']:
                size = part_option['size']
                parts = part_option['total_parts']
                time_estimate = f"~{parts * 3}-{parts * 5} min"
                table.add_row(str(size), str(parts), time_estimate)

            self.console.print("\n")
            self.console.print(table)

    def choose_analysis_approach(self, size_analysis: Dict[str, Any]) -> str:
        """Permite ao usuário escolher a abordagem de análise"""
        total_videos = size_analysis['total_videos']

        if total_videos <= 50:
            # Canal pequeno - análise completa automática
            self.console.print(f"\n✅ Canal pequeno detectado. Analisando todos os {total_videos} vídeos.")
            return 'complete'

        # Canal médio/grande - oferece opções
        self.console.print(f"\n🤔 Como você quer analisar esses {total_videos} vídeos?")

        choices = [
            "1️⃣ Análise completa (todos os vídeos)",
            "2️⃣ Análise por partes (divido em lotes menores)",
            "3️⃣ Análise dos mais recentes (últimos 30 vídeos)"
        ]

        for choice in choices:
            self.console.print(f"   {choice}")

        while True:
            choice = Prompt.ask("\n🎯 Digite sua escolha (1, 2 ou 3)")

            if choice == "1":
                return 'complete'
            elif choice == "2":
                return 'parts'
            elif choice == "3":
                return 'recent'
            else:
                self.console.print("❌ Escolha inválida. Digite 1, 2 ou 3.")

    def setup_parts_analysis(self, channel_info: Dict[str, Any], videos: List[Dict[str, Any]],
                           size_analysis: Dict[str, Any]):
        """Configura análise por partes"""
        self.console.print("\n🔧 Configurando análise por partes...")

        # Pergunta tamanho da parte
        part_options = size_analysis['suggestion']['parts']

        self.console.print("\n📏 Qual tamanho de parte você prefere?")
        for i, option in enumerate(part_options, 1):
            size = option['size']
            parts = option['total_parts']
            self.console.print(f"   {i}️⃣ {size} vídeos por parte ({parts} partes)")

        while True:
            choice = Prompt.ask(f"\n🎯 Digite sua escolha (1-{len(part_options)})")
            try:
                choice_idx = int(choice) - 1
                if 0 <= choice_idx < len(part_options):
                    part_size = part_options[choice_idx]['size']
                    break
                else:
                    self.console.print(f"❌ Escolha inválida. Digite um número de 1 a {len(part_options)}.")
            except ValueError:
                self.console.print("❌ Digite um número válido.")

        # Cria estrutura de partes
        self.console.print(f"\n🔄 Criando estrutura de {part_size} vídeos por parte...")
        parts_structure = self.parts_manager.create_parts_structure(channel_info, videos, part_size)

        # Mostra partes disponíveis e permite escolha
        self.show_available_parts(parts_structure)
        selected_part = self.select_part_to_analyze(parts_structure)

        if selected_part:
            self.analyze_selected_part(channel_info, parts_structure, selected_part)

    def show_available_parts(self, parts_structure: Dict[str, Any]):
        """Mostra partes disponíveis para análise"""
        self.console.print(f"\n📋 Estrutura criada! {parts_structure['total_parts']} partes disponíveis:")

        table = Table(title="🗂️ Partes Disponíveis")
        table.add_column("Parte", style="bold")
        table.add_column("Vídeos", style="cyan")
        table.add_column("Status", style="green")
        table.add_column("Período", style="yellow")

        for part in parts_structure['parts'][:10]:  # Mostra apenas as primeiras 10
            part_num = part['part_number']
            video_range = f"{part['start_video']}-{part['end_video']}"
            status = "⏳ Pendente" if part['status'] == 'pending' else "✅ Concluída"

            # Tenta determinar período baseado nos vídeos
            period = "Mais recentes" if part_num == 1 else f"Parte {part_num}"

            table.add_row(f"Parte {part_num}", video_range, status, period)

        if parts_structure['total_parts'] > 10:
            self.console.print(f"\n... e mais {parts_structure['total_parts'] - 10} partes")

        self.console.print("\n")
        self.console.print(table)

    def select_part_to_analyze(self, parts_structure: Dict[str, Any]) -> Optional[int]:
        """Permite ao usuário selecionar qual parte analisar"""
        total_parts = parts_structure['total_parts']

        self.console.print(f"\n🎯 Qual parte você quer analisar primeiro?")
        self.console.print(f"   💡 Dica: Parte 1 contém os vídeos mais recentes")

        while True:
            choice = Prompt.ask(f"\n📝 Digite o número da parte (1-{total_parts}) ou 'sair' para cancelar")

            if choice.lower() == 'sair':
                return None

            try:
                part_num = int(choice)
                if 1 <= part_num <= total_parts:
                    return part_num
                else:
                    self.console.print(f"❌ Número inválido. Digite um número de 1 a {total_parts}.")
            except ValueError:
                self.console.print("❌ Digite um número válido ou 'sair'.")

    def analyze_selected_part(self, channel_info: Dict[str, Any], parts_structure: Dict[str, Any],
                            part_number: int):
        """Analisa a parte selecionada"""
        self.console.print(f"\n🚀 Iniciando análise da Parte {part_number}...")

        # Atualiza status para "analisando"
        self.parts_manager.update_part_status(channel_info, part_number, 'analyzing')

        # Obtém vídeos da parte
        part_videos = self.parts_manager.get_part_videos(parts_structure, part_number)

        if not part_videos:
            self.console.print(f"❌ Erro ao carregar vídeos da parte {part_number}")
            return

        self.console.print(f"📊 Analisando {len(part_videos)} vídeos da Parte {part_number}")

        # Salva contexto da parte atual
        self.current_part = {
            'channel_info': channel_info,
            'parts_structure': parts_structure,
            'part_number': part_number,
            'videos': part_videos
        }

        # Continua com o fluxo normal (priorização, extração, etc.)
        self.console.print("✅ Parte configurada! Continuando com priorização...")

    def setup_recent_analysis(self, videos: List[Dict[str, Any]]):
        """Configura análise dos vídeos mais recentes"""
        recent_videos = videos[:30]  # Últimos 30 vídeos
        self.console.print(f"\n✅ Analisando os {len(recent_videos)} vídeos mais recentes")

        # Salva contexto
        self.current_analysis = {
            'type': 'recent',
            'videos': recent_videos
        }

    def setup_complete_analysis(self, videos: List[Dict[str, Any]]):
        """Configura análise completa"""
        self.console.print(f"\n✅ Analisando todos os {len(videos)} vídeos")

        # Salva contexto
        self.current_analysis = {
            'type': 'complete',
            'videos': videos
        }
