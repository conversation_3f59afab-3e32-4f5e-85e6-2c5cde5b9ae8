"""
Agente 1 - Cura<PERSON> de Conteúdo
Responsável por listar vídeos do canal e analisar títulos para relevância
"""
import json
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import yt_dlp
from loguru import logger
from crewai import Agent, Task, Crew

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from database import DatabaseManager
from ai_utils import LLaMAClient, AnalysisPrompts
from config import TARGET_CONTEXT_KEYWORDS, MAX_VIDEOS_PER_CHANNEL


class ContentCuratorAgent:
    """Agente curador de conteúdo do YouTube"""

    def __init__(self, db_manager: DatabaseManager, llama_client: LLaMAClient, project_dir: Path = None):
        self.db = db_manager
        self.llama = llama_client
        self.project_dir = project_dir or Path(".")  # Usa diretório atual se não especificado
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': True,
            'playlistend': MAX_VIDEOS_PER_CHANNEL,
            'geo_bypass': True,
            'geo_bypass_country': 'BR',
            'extractor_args': {
                'youtube': {
                    'lang': ['pt', 'pt-BR'],
                    'region': 'BR'
                }
            }
        }
    
    def extract_channel_videos(self, channel_url: str) -> List[Dict[str, Any]]:
        """Extrai lista de vídeos de um canal do YouTube com informações completas"""
        logger.info(f"Extraindo vídeos do canal: {channel_url}")

        videos = []
        try:
            # Converte URL do canal para URL de vídeos se necessário
            if "@" in channel_url:
                videos_url = channel_url + "/videos"
            else:
                videos_url = channel_url

            # ETAPA 1: Extrai lista básica de vídeos (rápido)
            logger.info("Etapa 1: Extraindo lista básica de vídeos...")
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                channel_info = ydl.extract_info(videos_url, download=False)

                video_ids = []
                if 'entries' in channel_info:
                    for entry in channel_info['entries']:
                        if entry and 'id' in entry:
                            video_ids.append(entry['id'])
                            if len(video_ids) >= MAX_VIDEOS_PER_CHANNEL:
                                break

            logger.info(f"Encontrados {len(video_ids)} vídeos. Extraindo detalhes...")

            # ETAPA 2: Extrai detalhes de cada vídeo (mais lento, mas completo)
            for i, video_id in enumerate(video_ids, 1):
                logger.info(f"Extraindo detalhes {i}/{len(video_ids)}: {video_id}")

                video_details = self._extract_video_details_enhanced(video_id)
                if video_details:
                    videos.append(video_details)

                # Log de progresso a cada 5 vídeos
                if i % 5 == 0:
                    logger.info(f"Progresso: {i}/{len(video_ids)} vídeos processados")

            logger.info(f"Extraídos {len(videos)} vídeos com detalhes completos")
            return videos

        except Exception as e:
            logger.error(f"Erro ao extrair vídeos do canal: {e}")
            # Tenta método alternativo
            return self._extract_channel_videos_alternative(channel_url)
    
    def _process_video_entry(self, entry: Dict) -> Optional[Dict[str, Any]]:
        """Processa entrada de vídeo já extraída"""
        try:
            if not entry or 'id' not in entry:
                return None

            # Se já temos informações detalhadas, usa elas
            if 'title' in entry and 'description' in entry:
                return {
                    'video_id': entry['id'],
                    'title': entry.get('title', ''),
                    'description': entry.get('description', ''),
                    'url': f"https://www.youtube.com/watch?v={entry['id']}",
                    'view_count': entry.get('view_count', 0),
                    'like_count': entry.get('like_count', 0),
                    'comment_count': entry.get('comment_count', 0),
                    'upload_date': entry.get('upload_date', ''),
                    'duration': entry.get('duration_string', ''),
                    'thumbnail': entry.get('thumbnail', '')
                }
            else:
                # Se não temos detalhes, extrai do vídeo individual
                return self._extract_video_details(entry['id'])

        except Exception as e:
            logger.warning(f"Erro ao processar entrada do vídeo: {e}")
            return None

    def _extract_video_details_enhanced(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Extrai detalhes completos de um vídeo específico"""
        try:
            video_url = f"https://www.youtube.com/watch?v={video_id}"

            # Configuração otimizada para extrair mais informações
            ydl_opts_detailed = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,  # IMPORTANTE: False para extrair detalhes completos
                'geo_bypass': True,
                'geo_bypass_country': 'BR',
                'writesubtitles': False,
                'writeautomaticsub': False,
                'extractor_args': {
                    'youtube': {
                        'lang': ['pt', 'pt-BR'],
                        'region': 'BR'
                    }
                }
            }

            with yt_dlp.YoutubeDL(ydl_opts_detailed) as ydl:
                info = ydl.extract_info(video_url, download=False)

                # Formatar data de upload
                upload_date = info.get('upload_date', '')
                if upload_date and len(upload_date) == 8:  # YYYYMMDD
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(upload_date, '%Y%m%d')
                        upload_date = date_obj.strftime('%Y-%m-%d')
                    except:
                        pass

                # Formatar duração
                duration = info.get('duration', 0)
                duration_str = ''
                if duration:
                    minutes = duration // 60
                    seconds = duration % 60
                    duration_str = f"{minutes}:{seconds:02d}"

                return {
                    'video_id': video_id,
                    'title': info.get('title', ''),
                    'description': info.get('description', '')[:500] + '...' if info.get('description', '') and len(info.get('description', '')) > 500 else info.get('description', ''),
                    'url': video_url,
                    'view_count': info.get('view_count', 0) or 0,
                    'like_count': info.get('like_count', 0) or 0,
                    'comment_count': info.get('comment_count', 0) or 0,
                    'upload_date': upload_date,
                    'duration': duration_str,
                    'thumbnail': info.get('thumbnail', ''),
                    'uploader': info.get('uploader', ''),
                    'channel_id': info.get('channel_id', ''),
                    'channel_url': info.get('channel_url', '')
                }

        except Exception as e:
            logger.warning(f"Erro ao extrair detalhes do vídeo {video_id}: {e}")
            # Retorna informações básicas se a extração detalhada falhar
            video_url = f"https://www.youtube.com/watch?v={video_id}"
            return {
                'video_id': video_id,
                'title': f'Vídeo {video_id}',
                'description': '',
                'url': video_url,
                'view_count': 0,
                'like_count': 0,
                'comment_count': 0,
                'upload_date': '',
                'duration': '',
                'thumbnail': '',
                'uploader': '',
                'channel_id': '',
                'channel_url': ''
            }

    def _extract_video_details(self, video_id: str) -> Optional[Dict[str, Any]]:
        """Método legado - mantido para compatibilidade"""
        return self._extract_video_details_enhanced(video_id)

    def _extract_channel_videos_alternative(self, channel_url: str) -> List[Dict[str, Any]]:
        """Método alternativo para extrair vídeos do canal"""
        logger.info("Tentando método alternativo de extração")

        videos = []
        try:
            # Tenta diferentes formatos de URL
            urls_to_try = [
                channel_url,
                channel_url + "/videos",
                channel_url.replace("@", "c/") if "@" in channel_url else channel_url,
            ]

            for url in urls_to_try:
                try:
                    logger.info(f"Tentando URL: {url}")

                    ydl_opts_simple = {
                        'quiet': True,
                        'no_warnings': True,
                        'extract_flat': True,
                        'playlistend': 10,  # Limita para evitar timeout
                        'geo_bypass': True,
                        'geo_bypass_country': 'BR',
                        'extractor_args': {
                            'youtube': {
                                'lang': ['pt', 'pt-BR'],
                                'region': 'BR'
                            }
                        }
                    }

                    with yt_dlp.YoutubeDL(ydl_opts_simple) as ydl:
                        info = ydl.extract_info(url, download=False)

                        if info and 'entries' in info:
                            for entry in info['entries'][:MAX_VIDEOS_PER_CHANNEL]:
                                if entry and 'id' in entry:
                                    video_info = {
                                        'video_id': entry['id'],
                                        'title': entry.get('title', f'Vídeo {entry["id"]}'),
                                        'description': entry.get('description', ''),
                                        'url': f"https://www.youtube.com/watch?v={entry['id']}",
                                        'view_count': entry.get('view_count', 0),
                                        'like_count': 0,
                                        'comment_count': 0,
                                        'upload_date': entry.get('upload_date', ''),
                                        'duration': entry.get('duration_string', ''),
                                        'uploader': entry.get('uploader', ''),
                                        'channel_id': entry.get('channel_id', ''),
                                        'channel_url': entry.get('channel_url', '')
                                    }
                                    videos.append(video_info)

                            if videos:
                                logger.info(f"Método alternativo extraiu {len(videos)} vídeos")
                                return videos

                except Exception as e:
                    logger.debug(f"URL {url} falhou: {e}")
                    continue

            logger.warning("Todos os métodos de extração falharam")
            return []

        except Exception as e:
            logger.error(f"Erro no método alternativo: {e}")
            return []

    def analyze_video_relevance(self, video: Dict[str, Any], context_keywords: List[str] = None,
                               nicho: str = None) -> Dict[str, Any]:
        """Analisa se um vídeo é relevante para o contexto usando IA contextual"""
        title = video.get('title', '')
        description = video.get('description', '')

        # Usa palavras-chave personalizadas ou padrão
        keywords_to_use = context_keywords or TARGET_CONTEXT_KEYWORDS

        # Usa IA para analisar relevância contextual
        prompt = self._create_contextual_analysis_prompt(title, description, keywords_to_use, nicho)
        response = self.llama.generate_response(prompt, temperature=0.3)
        
        try:
            # Tenta extrair JSON da resposta
            analysis = self._extract_json_from_response(response)
            if analysis:
                return {
                    'is_relevant': analysis.get('relevante', False),
                    'relevance_score': analysis.get('score', 0.0),
                    'context_analysis': analysis.get('contexto_identificado', ''),
                    'keywords_found': analysis.get('palavras_chave_encontradas', [])
                }
        except Exception as e:
            logger.warning(f"Erro ao analisar relevância do vídeo '{title}': {e}")

        # Fallback: análise simples baseada em palavras-chave
        logger.debug(f"Usando análise simples para: {title}")
        return self._simple_relevance_analysis(title)

    def _create_contextual_analysis_prompt(self, title: str, description: str,
                                         keywords: List[str], nicho: str = None) -> str:
        """Cria prompt para análise contextual inteligente"""
        keywords_str = ", ".join(keywords)

        context_info = f"Nicho/Contexto: {nicho}" if nicho else "Contexto geral"

        return f"""Analise se este vídeo é relevante para o contexto especificado.

{context_info}
Palavras-chave de referência: {keywords_str}

Título: "{title}"
Descrição: "{description[:200]}..."

IMPORTANTE: Não busque apenas palavras-chave literais. Analise o SIGNIFICADO e CONTEXTO do título/descrição.

Exemplos de análise contextual:
- "Como lidar com crises de comportamento" → Relevante para "pais de crianças autistas" (mesmo sem palavra "autismo")
- "Dicas para pais de crianças especiais" → Relevante para "pais de crianças autistas"
- "Terapia ABA funciona?" → Relevante para "autismo" (ABA é terapia para autismo)
- "Receita de bolo de chocolate" → Não relevante para "autismo"

Retorne APENAS um JSON válido:
{{
    "relevante": true/false,
    "score_relevancia": 0.0-1.0,
    "justificativa": "Explique por que é ou não relevante",
    "palavras_encontradas": ["palavra1", "palavra2"],
    "contexto_identificado": "Que contexto/tema você identificou no vídeo"
}}"""

    def _extract_json_from_response(self, response: str) -> Optional[Dict]:
        """Tenta extrair JSON de uma resposta que pode conter texto extra"""
        if not response:
            return None

        # Tenta JSON direto primeiro
        try:
            return json.loads(response.strip())
        except:
            pass

        # Procura por JSON entre chaves
        import re
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except:
                continue

        # Procura por linhas que começam com {
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('{') and line.endswith('}'):
                try:
                    return json.loads(line)
                except:
                    continue

        return None

    def _simple_relevance_analysis(self, title: str) -> Dict[str, Any]:
        """Análise simples de relevância baseada em palavras-chave"""
        title_lower = title.lower()
        found_keywords = []
        
        for keyword in TARGET_CONTEXT_KEYWORDS:
            if keyword.lower() in title_lower:
                found_keywords.append(keyword)
        
        relevance_score = len(found_keywords) / len(TARGET_CONTEXT_KEYWORDS)
        is_relevant = relevance_score > 0.1  # Pelo menos 10% das palavras-chave
        
        return {
            'is_relevant': is_relevant,
            'relevance_score': relevance_score,
            'context_analysis': f"Encontradas palavras-chave: {', '.join(found_keywords)}" if found_keywords else "Nenhuma palavra-chave relevante encontrada",
            'keywords_found': found_keywords
        }
    
    def save_videos_to_database(self, channel_url: str, videos: List[Dict[str, Any]],
                               context_keywords: List[str] = None, nicho: str = None) -> int:
        """Salva vídeos no banco de dados com análise contextual"""
        # Insere ou busca canal
        channel_name = videos[0].get('uploader', '') if videos else ''
        channel_id_yt = videos[0].get('channel_id', '') if videos else ''

        channel_id = self.db.insert_channel(channel_url, channel_name, channel_id_yt)

        relevant_count = 0
        for video in videos:
            # Analisa relevância com contexto personalizado
            analysis = self.analyze_video_relevance(video, context_keywords, nicho)
            
            # Adiciona dados do canal
            video['channel_id'] = channel_id
            
            # Salva vídeo no banco
            video_db_id = self.db.insert_video(video)
            
            # Atualiza análise de relevância
            self.db.update_video_analysis(
                video['video_id'],
                analysis['relevance_score'],
                analysis['is_relevant'],
                analysis['context_analysis']
            )
            
            if analysis['is_relevant']:
                relevant_count += 1
                logger.info(f"Vídeo relevante encontrado: {video['title']}")
        
        logger.info(f"Salvos {len(videos)} vídeos, {relevant_count} relevantes")
        return channel_id
    
    def export_relevant_videos(self, channel_id: int, output_file: str = None) -> str:
        """Exporta vídeos relevantes para arquivo JSON"""
        if not output_file:
            # Usa pasta output do projeto específico
            output_dir = self.project_dir / "output"
            output_dir.mkdir(exist_ok=True)
            output_file = output_dir / "relevant_videos.json"
        
        relevant_videos = self.db.get_relevant_videos(channel_id)
        
        export_data = {
            'channel_id': channel_id,
            'total_relevant_videos': len(relevant_videos),
            'extraction_date': str(datetime.now()),
            'videos': relevant_videos
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Vídeos relevantes exportados para: {output_file}")
        return str(output_file)

    def extract_and_save_videos_only(self, channel_url: str) -> Dict[str, Any]:
        """ETAPA 2: Apenas extrai vídeos e salva, SEM análise de IA"""
        logger.info(f"Extraindo todos os vídeos do canal: {channel_url}")

        try:
            # 1. Extrai vídeos do canal
            logger.info("Passo 1: Extraindo vídeos...")
            videos = self.extract_channel_videos(channel_url)
            logger.info(f"Extraídos {len(videos) if videos else 0} vídeos")

            if not videos:
                return {'success': False, 'error': 'Nenhum vídeo encontrado no canal'}

            # 2. Salva canal no banco (sem análise)
            logger.info("Passo 2: Salvando canal no banco...")
            channel_id = self.db.insert_channel(channel_url)
            logger.info(f"Canal salvo com ID: {channel_id}")

            # 3. Salva vídeos no banco SEM análise de relevância
            logger.info("Passo 3: Salvando vídeos no banco...")
            for i, video in enumerate(videos, 1):
                video['channel_id'] = channel_id
                self.db.insert_video(video)
                if i % 10 == 0:  # Log a cada 10 vídeos
                    logger.info(f"Salvos {i}/{len(videos)} vídeos...")

            logger.info(f"Todos os {len(videos)} vídeos salvos no banco")

        except Exception as e:
            logger.error(f"Erro durante extração: {e}")
            return {'success': False, 'error': f'Erro durante extração: {str(e)}'}

        try:
            # 4. Exporta todos os vídeos para JSON
            logger.info("Passo 4: Exportando para JSON...")
            import json
            from pathlib import Path
            from datetime import datetime

            # Usa pasta output do projeto específico
            output_dir = self.project_dir / "output"
            output_dir.mkdir(exist_ok=True)

            json_file = output_dir / f"todos_videos_canal_{channel_id}.json"

            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'channel_url': channel_url,
                    'channel_id': channel_id,
                    'total_videos': len(videos),
                    'extraction_date': str(datetime.now()),
                    'videos': videos
                }, f, ensure_ascii=False, indent=2)

            logger.info(f"JSON exportado para: {json_file}")

            result = {
                'success': True,
                'channel_id': channel_id,
                'total_videos': len(videos),
                'json_file': str(json_file),
                'videos_sample': videos[:5]  # Mostra apenas 5 primeiros
            }

            logger.info(f"Extração concluída com sucesso: {len(videos)} vídeos")
            return result

        except Exception as e:
            logger.error(f"Erro ao exportar JSON: {e}")
            return {'success': False, 'error': f'Erro ao exportar JSON: {str(e)}'}

    def run_curation_workflow(self, channel_url: str, context_keywords: List[str] = None,
                             nicho: str = None) -> Dict[str, Any]:
        """Executa o workflow completo de curadoria com contexto personalizado"""
        logger.info(f"Iniciando workflow de curadoria para nicho: {nicho}")

        # 1. Extrai vídeos do canal
        videos = self.extract_channel_videos(channel_url)
        if not videos:
            return {'success': False, 'error': 'Nenhum vídeo encontrado'}

        # 2. Salva no banco de dados com análise de relevância contextual
        channel_id = self.save_videos_to_database(channel_url, videos, context_keywords, nicho)
        
        # 3. Exporta vídeos relevantes
        export_file = self.export_relevant_videos(channel_id)
        
        # 4. Estatísticas finais
        relevant_videos = self.db.get_relevant_videos(channel_id)
        
        result = {
            'success': True,
            'channel_id': channel_id,
            'total_videos': len(videos),
            'relevant_videos': len(relevant_videos),
            'relevance_rate': len(relevant_videos) / len(videos) if videos else 0,
            'export_file': export_file,
            'top_relevant_videos': [
                {
                    'title': v['title'],
                    'relevance_score': v['context_relevance_score'],
                    'url': v['url']
                }
                for v in relevant_videos[:5]
            ]
        }
        
        logger.info(f"Curadoria concluída: {result['relevant_videos']}/{result['total_videos']} vídeos relevantes")
        return result


def create_content_curator_crew_agent() -> Agent:
    """Cria agente CrewAI para curadoria de conteúdo"""
    return Agent(
        role='Curador de Conteúdo YouTube',
        goal='Identificar e catalogar vídeos relevantes sobre parentalidade e família',
        backstory="""Você é um especialista em análise de conteúdo do YouTube com foco em 
        parentalidade, educação infantil e dinâmicas familiares. Sua missão é identificar 
        vídeos que contenham discussões valiosas sobre a experiência de ser pai ou mãe.""",
        verbose=True,
        allow_delegation=False
    )


def create_content_analysis_task(agent: Agent, channel_url: str) -> Task:
    """Cria task CrewAI para análise de conteúdo"""
    return Task(
        description=f"""
        Analise o canal do YouTube: {channel_url}
        
        Suas responsabilidades:
        1. Extrair lista completa de vídeos do canal
        2. Analisar títulos para identificar relevância com parentalidade
        3. Classificar vídeos por relevância ao contexto familiar
        4. Salvar resultados estruturados para próximas etapas
        
        Foque em vídeos que discutam:
        - Desafios da parentalidade
        - Educação de filhos
        - Dinâmicas familiares
        - Desenvolvimento infantil
        - Experiências de pais e mães
        """,
        agent=agent,
        expected_output="Relatório com vídeos categorizados por relevância e análise contextual"
    )
