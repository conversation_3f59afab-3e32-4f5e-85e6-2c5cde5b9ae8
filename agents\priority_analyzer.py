"""
Agente 2 - Analisador de Prioridade
Responsável por ordenar vídeos por relevância e prioridade para análise
"""
import json
import math
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent))

from loguru import logger
from crewai import Agent, Task

from database import DatabaseManager
from ai_utils import LLaMAClient, AnalysisPrompts
# OUTPUT_DIR removido - cada projeto tem sua estrutura


class PriorityAnalyzerAgent:
    """Agente analisador de prioridade de vídeos"""
    
    def __init__(self, db_manager: DatabaseManager, llama_client: LLaMAClient):
        self.db = db_manager
        self.llama = llama_client
    
    def calculate_engagement_score(self, video: Dict[str, Any]) -> float:
        """Calcula score de engajamento baseado em métricas do vídeo"""
        view_count = video.get('view_count', 0)
        comment_count = video.get('comment_count', 0)
        like_count = video.get('like_count', 0)
        
        if view_count == 0:
            return 0.0
        
        # Normaliza métricas
        comment_rate = comment_count / view_count if view_count > 0 else 0
        like_rate = like_count / view_count if view_count > 0 else 0
        
        # Score baseado em engajamento (comentários são mais importantes para nossa análise)
        engagement_score = (
            comment_rate * 0.6 +  # Comentários são cruciais
            like_rate * 0.3 +     # Likes indicam interesse
            min(math.log10(view_count + 1) / 7, 1.0) * 0.1  # Views normalizadas
        )
        
        return min(engagement_score, 1.0)
    
    def calculate_recency_score(self, upload_date: str) -> float:
        """Calcula score baseado na recência do vídeo"""
        if not upload_date:
            return 0.5  # Score neutro para datas desconhecidas
        
        try:
            # Converte data do formato YYYYMMDD
            video_date = datetime.strptime(upload_date, '%Y%m%d')
            current_date = datetime.now()
            
            # Diferença em dias
            days_diff = (current_date - video_date).days
            
            # Score decai exponencialmente, mas vídeos muito antigos ainda têm valor
            if days_diff <= 30:
                return 1.0  # Vídeos recentes (último mês)
            elif days_diff <= 180:
                return 0.8  # Últimos 6 meses
            elif days_diff <= 365:
                return 0.6  # Último ano
            elif days_diff <= 730:
                return 0.4  # Últimos 2 anos
            else:
                return 0.2  # Mais antigos
                
        except Exception as e:
            logger.warning(f"Erro ao processar data {upload_date}: {e}")
            return 0.5
    
    def analyze_title_complexity(self, title: str) -> float:
        """Analisa complexidade e potencial de discussão do título"""
        if not title:
            return 0.0
        
        # Indicadores de conteúdo que gera discussão
        discussion_indicators = [
            'como', 'por que', 'porque', 'dicas', 'segredo', 'erro', 'problema',
            'dificuldade', 'desafio', 'experiência', 'história', 'relato',
            'confissão', 'verdade', 'realidade', 'mito', 'dúvida', 'ajuda'
        ]
        
        emotional_indicators = [
            'amor', 'medo', 'preocupação', 'ansiedade', 'felicidade', 'tristeza',
            'orgulho', 'culpa', 'frustração', 'alegria', 'emoção', 'sentimento'
        ]
        
        title_lower = title.lower()
        
        # Conta indicadores
        discussion_count = sum(1 for indicator in discussion_indicators if indicator in title_lower)
        emotional_count = sum(1 for indicator in emotional_indicators if indicator in title_lower)
        
        # Pontos por interrogação e exclamação
        question_marks = title.count('?')
        exclamation_marks = title.count('!')
        
        # Score baseado em complexidade
        complexity_score = (
            discussion_count * 0.3 +
            emotional_count * 0.2 +
            question_marks * 0.2 +
            exclamation_marks * 0.1 +
            min(len(title.split()) / 10, 0.2)  # Títulos mais longos podem ser mais específicos
        )
        
        return min(complexity_score, 1.0)
    
    def calculate_priority_score(self, video: Dict[str, Any]) -> float:
        """Calcula score final de prioridade"""
        relevance_score = video.get('context_relevance_score', 0.0)
        engagement_score = self.calculate_engagement_score(video)
        recency_score = self.calculate_recency_score(video.get('upload_date', ''))
        complexity_score = self.analyze_title_complexity(video.get('title', ''))
        
        # Pesos para diferentes fatores
        priority_score = (
            relevance_score * 0.4 +    # Relevância é mais importante
            engagement_score * 0.3 +   # Engajamento indica discussões ativas
            complexity_score * 0.2 +   # Complexidade indica potencial de análise
            recency_score * 0.1        # Recência é menos importante
        )
        
        return min(priority_score, 1.0)
    
    def ai_prioritize_videos(self, videos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Usa IA para analisar e priorizar vídeos"""
        if not videos:
            return {'ranking': [], 'scores': [], 'justificativas': []}
        
        # Prepara dados para IA (limita a 20 vídeos para não sobrecarregar)
        videos_sample = videos[:20] if len(videos) > 20 else videos
        
        prompt = AnalysisPrompts.prioritize_videos(videos_sample)
        response = self.llama.generate_response(prompt, temperature=0.3)
        
        try:
            # Tenta extrair JSON da resposta
            ai_analysis = self._extract_json_from_response(response)
            if ai_analysis and 'ranking' in ai_analysis:
                return ai_analysis
        except Exception as e:
            logger.warning(f"Erro na análise de IA para priorização: {e}")

        # Fallback: ordenação simples por relevância
        logger.debug("Usando priorização simples como fallback")
        return self._simple_prioritization(videos_sample)

    def _extract_json_from_response(self, response: str) -> Optional[Dict]:
        """Tenta extrair JSON de uma resposta que pode conter texto extra"""
        if not response:
            return None

        # Tenta JSON direto primeiro
        try:
            return json.loads(response.strip())
        except:
            pass

        # Procura por JSON entre chaves
        import re
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, response, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except:
                continue

        # Procura por linhas que começam com {
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('{') and line.endswith('}'):
                try:
                    return json.loads(line)
                except:
                    continue

        return None

    def _simple_prioritization(self, videos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Priorização simples baseada em scores calculados"""
        scores = []
        for video in videos:
            score = self.calculate_priority_score(video)
            scores.append(score)
        
        # Cria ranking baseado nos scores
        indexed_scores = [(i, score) for i, score in enumerate(scores)]
        indexed_scores.sort(key=lambda x: x[1], reverse=True)
        
        ranking = [i + 1 for i, _ in indexed_scores]  # +1 porque ranking começa em 1
        sorted_scores = [score for _, score in indexed_scores]
        justificativas = [f"Score calculado: {score:.3f}" for score in sorted_scores]
        
        return {
            'ranking': ranking,
            'scores': sorted_scores,
            'justificativas': justificativas
        }
    
    def prioritize_channel_videos(self, channel_id: int) -> Dict[str, Any]:
        """Prioriza todos os vídeos relevantes de um canal"""
        logger.info(f"Priorizando vídeos do canal {channel_id}")
        
        # Busca vídeos relevantes
        relevant_videos = self.db.get_relevant_videos(channel_id)
        
        if not relevant_videos:
            logger.warning("Nenhum vídeo relevante encontrado para priorização")
            return {'success': False, 'error': 'Nenhum vídeo relevante encontrado'}
        
        # Calcula scores de prioridade para todos os vídeos
        for video in relevant_videos:
            priority_score = self.calculate_priority_score(video)
            
            # Atualiza no banco de dados
            self.db.update_video_analysis(
                video['video_id'],
                video['context_relevance_score'],
                video['is_relevant'],
                video['context_analysis'],
                priority_score
            )
        
        # Usa IA para análise adicional dos top vídeos
        ai_analysis = self.ai_prioritize_videos(relevant_videos)
        
        # Busca vídeos atualizados ordenados por prioridade
        prioritized_videos = self.db.get_relevant_videos(channel_id, limit=50)
        
        result = {
            'success': True,
            'channel_id': channel_id,
            'total_videos_analyzed': len(relevant_videos),
            'ai_analysis': ai_analysis,
            'top_priority_videos': [
                {
                    'title': v['title'],
                    'priority_score': v['priority_score'],
                    'relevance_score': v['context_relevance_score'],
                    'url': v['url'],
                    'view_count': v['view_count'],
                    'comment_count': v['comment_count']
                }
                for v in prioritized_videos[:10]
            ]
        }
        
        logger.info(f"Priorização concluída para {len(relevant_videos)} vídeos")
        return result
    
    def export_prioritized_videos(self, channel_id: int, output_file: str = None) -> str:
        """Exporta vídeos priorizados para arquivo"""
        if not output_file:
            output_file = "prioritized_videos.json"
        
        prioritized_videos = self.db.get_relevant_videos(channel_id)
        
        export_data = {
            'channel_id': channel_id,
            'total_prioritized_videos': len(prioritized_videos),
            'prioritization_date': str(datetime.now()),
            'videos': prioritized_videos
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Vídeos priorizados exportados para: {output_file}")
        return str(output_file)
    
    def run_prioritization_workflow(self, channel_id: int) -> Dict[str, Any]:
        """Executa workflow completo de priorização"""
        logger.info("Iniciando workflow de priorização")
        
        # 1. Prioriza vídeos
        result = self.prioritize_channel_videos(channel_id)
        
        if not result['success']:
            return result
        
        # 2. Exporta resultados
        export_file = self.export_prioritized_videos(channel_id)
        result['export_file'] = export_file
        
        logger.info("Workflow de priorização concluído")
        return result


def create_priority_analyzer_crew_agent() -> Agent:
    """Cria agente CrewAI para análise de prioridade"""
    return Agent(
        role='Analisador de Prioridade de Conteúdo',
        goal='Priorizar vídeos por potencial de análise e relevância para extração de insights',
        backstory="""Você é um especialista em análise de dados e métricas de engajamento 
        do YouTube. Sua expertise está em identificar quais vídeos têm maior potencial 
        para gerar discussões valiosas sobre parentalidade, baseando-se em métricas de 
        engajamento, relevância do conteúdo e potencial de análise.""",
        verbose=True,
        allow_delegation=False
    )


def create_prioritization_task(agent: Agent, channel_id: int) -> Task:
    """Cria task CrewAI para priorização"""
    return Task(
        description=f"""
        Analise e priorize os vídeos relevantes do canal ID: {channel_id}
        
        Suas responsabilidades:
        1. Calcular scores de engajamento baseados em views, likes e comentários
        2. Analisar potencial de discussão baseado nos títulos
        3. Considerar recência e relevância contextual
        4. Criar ranking final priorizando vídeos com maior potencial de insights
        5. Justificar decisões de priorização
        
        Critérios de priorização:
        - Vídeos com mais comentários (indicam discussão ativa)
        - Títulos que sugerem experiências pessoais ou desafios
        - Conteúdo que gera debate sobre parentalidade
        - Equilíbrio entre relevância e engajamento
        """,
        agent=agent,
        expected_output="Ranking priorizado de vídeos com justificativas e scores detalhados"
    )
